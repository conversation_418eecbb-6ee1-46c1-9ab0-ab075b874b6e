const fs = require('fs');
const path = require('path');
const { app } = require('electron');

// 读取卡密验证脚本
function getKamiyanScript() {
  try {
    // 首先尝试从资源目录读取
    const resourcePath = path.join(process.resourcesPath, 'resources', 'kamiyanzheng.js');
    
    // 如果在开发环境，则从项目目录读取
    const devPath = path.join(__dirname, '../resources/kamiyanzheng.js');
    
    // 尝试从上级目录读取（针对打包后的路径结构）
    const parentPath = path.join(__dirname, '../../kamiyanzheng.js');
    
    // 添加更多可能的路径
    const additionalPaths = [
      path.join(__dirname, '../kamiyanzheng.js'),
      path.join(app ? app.getAppPath() : __dirname, 'kamiyanzheng.js'),
      path.join(app ? app.getAppPath() : __dirname, 'resources', 'kamiyanzheng.js')
    ];
    
    // 合并所有可能的路径
    const allPaths = [resourcePath, devPath, parentPath, ...additionalPaths];
    
    // 查找第一个存在的文件
    let scriptPath;
    for (const p of allPaths) {
      if (fs.existsSync(p)) {
        scriptPath = p;
        console.log(`从路径加载卡密验证脚本: ${p}`);
        break;
      }
    }
    
    if (!scriptPath) {
      throw new Error('卡密验证脚本文件不存在');
    }
    
    const script = fs.readFileSync(scriptPath, 'utf-8');
    console.log(`成功读取卡密验证脚本，大小: ${script.length} 字节`);
    return script;
  } catch (error) {
    console.error('读取卡密验证脚本失败:', error);
    return null;
  }
}

// 获取xinkami.js脚本
function getXinkamiScript() {
  try {
    // 查找xinkami.js的所有可能路径
    const xinkamiPaths = [
      path.join(process.resourcesPath, 'resources', 'xinkami.js'),
      path.join(__dirname, '../resources/xinkami.js'),
      path.join(__dirname, '../../xinkami.js'),
      path.join(__dirname, '../xinkami.js'),
      path.join(app ? app.getAppPath() : __dirname, 'xinkami.js'),
      path.join(app ? app.getAppPath() : __dirname, 'resources', 'xinkami.js')
    ];
    
    // 查找第一个存在的文件
    let scriptPath;
    for (const p of xinkamiPaths) {
      if (fs.existsSync(p)) {
        scriptPath = p;
        console.log(`从路径加载xinkami.js脚本: ${p}`);
        break;
      }
    }
    
    if (!scriptPath) {
      console.error('xinkami.js脚本文件不存在');
      return null;
    }
    
    const script = fs.readFileSync(scriptPath, 'utf-8');
    console.log(`成功读取xinkami.js脚本，大小: ${script.length} 字节`);
    
    // 修改脚本中的API端点，将xinkami.js替换为verify.php
    const modifiedScript = script.replace(
      /https:\/\/xiaomeihuakefu\.cn\/api\/xinkami\.js/g, 
      'https://xiaomeihuakefu.cn/api/verify.php'
    );
    
    console.log('已修改脚本中的API端点为verify.php');
    
    return modifiedScript;
  } catch (error) {
    console.error('读取xinkami.js脚本失败:', error);
    return null;
  }
}

// 获取完整的tampermonkey浏览器脚本
function getTampermonkeyScript() {
  try {
    // 查找tampermonkey浏览器脚本的所有可能路径
    const tampermonkeyPaths = [
      path.join(process.resourcesPath, 'resources', 'tampermonkey浏览器脚本.js'),
      path.join(__dirname, '../resources/tampermonkey浏览器脚本.js'),
      path.join(__dirname, '../../tampermonkey浏览器脚本.js'),
      path.join(__dirname, '../tampermonkey浏览器脚本.js'),
      path.join(app ? app.getAppPath() : __dirname, 'tampermonkey浏览器脚本.js'),
      path.join(app ? app.getAppPath() : __dirname, 'resources', 'tampermonkey浏览器脚本.js')
    ];

    // 查找第一个存在的文件
    let scriptPath;
    for (const p of tampermonkeyPaths) {
      if (fs.existsSync(p)) {
        scriptPath = p;
        console.log(`从路径加载tampermonkey浏览器脚本: ${p}`);
        break;
      }
    }

    if (!scriptPath) {
      console.error('tampermonkey浏览器脚本文件不存在');
      return null;
    }

    const script = fs.readFileSync(scriptPath, 'utf-8');
    console.log(`成功读取tampermonkey浏览器脚本，大小: ${script.length} 字节`);
    return script;
  } catch (error) {
    console.error('读取tampermonkey浏览器脚本失败:', error);
    return null;
  }
}

// 获取AI知识库脚本
function getAIKnowledgeScript() {
  try {
    // AI知识库脚本内容
    const aiKnowledgeScript = `
      // ==UserScript==
      // @name         小梅花AI知识库助手
      // @namespace    http://xiaomeihuakefu.cn/
      // @version      1.0.0
      // @description  AI知识库智能助手，提供知识库管理和AI对话功能
      // <AUTHOR>
      // @match        *://*/*
      // @grant        none
      // @run-at       document-start
      // ==/UserScript==

      (function() {
          'use strict';

          console.log('小梅花AI知识库助手已加载');

          // 检查是否在AI知识库页面
          function isAIKnowledgePage() {
              return window.location.href.includes('ai-knowledge.html') ||
                     window.location.pathname.includes('ai-knowledge') ||
                     document.title.includes('AI知识库');
          }

          // 等待页面加载完成
          if (document.readyState === 'loading') {
              document.addEventListener('DOMContentLoaded', initAIKnowledge);
          } else {
              initAIKnowledge();
          }

          function initAIKnowledge() {
              console.log('初始化AI知识库助手...');

              // 如果在AI知识库页面，注入特殊功能
              if (isAIKnowledgePage()) {
                  console.log('检测到AI知识库页面，注入专用功能...');
                  injectAIKnowledgeFeatures();
              } else {
                  // 在其他页面注入通用AI助手
                  injectGeneralAIAssistant();
              }
          }

          // 注入AI知识库专用功能
          function injectAIKnowledgeFeatures() {
              console.log('🤖 注入AI知识库专用功能...');

              // 不再清空页面内容，而是增强现有功能
              // 创建隐藏的功能容器
              const container = document.createElement('div');
              container.id = 'ai-knowledge-container';
              container.style.cssText = \`
                  position: fixed;
                  top: -9999px;
                  left: -9999px;
                  width: 1px;
                  height: 1px;
                  opacity: 0;
                  visibility: hidden;
                  pointer-events: none;
              \`;
              document.body.appendChild(container);

              // 通知页面Tampermonkey脚本已注入
              if (window.aiKnowledgeAPI && typeof window.aiKnowledgeAPI.addKnowledgeItem === 'function') {
                  setTimeout(() => {
                      window.aiKnowledgeAPI.addKnowledgeItem(
                          '🚀 Tampermonkey脚本已激活',
                          'AI知识库增强功能已成功注入，所有高级功能现已可用。',
                          '#28a745'
                      );
                  }, 1000);
              }

              // 创建AI知识库对象
              window.xiaomeihuaAIKnowledge = {
                  version: '1.0.0',
                  initialized: true,

                  // 初始化函数
                  init: function() {
                      this.setupAPI();
                      this.bindEvents();
                      this.setupTampermonkeyAPIs();
                      console.log('✅ 小梅花AI知识库助手初始化完成');
                  },

                  // 设置Tampermonkey API
                  setupTampermonkeyAPIs: function() {
                      console.log('🔧 设置Tampermonkey API...');

                      // 模拟Tampermonkey API
                      if (!window.GM_setValue) {
                          window.GM_setValue = function(key, value) {
                              localStorage.setItem('xiaomeihua_' + key, JSON.stringify(value));
                              return true;
                          };
                      }

                      if (!window.GM_getValue) {
                          window.GM_getValue = function(key, defaultValue) {
                              const value = localStorage.getItem('xiaomeihua_' + key);
                              return value !== null ? JSON.parse(value) : defaultValue;
                          };
                      }

                      if (!window.GM_deleteValue) {
                          window.GM_deleteValue = function(key) {
                              localStorage.removeItem('xiaomeihua_' + key);
                              return true;
                          };
                      }

                      if (!window.GM_xmlhttpRequest) {
                          window.GM_xmlhttpRequest = function(options) {
                              const xhr = new XMLHttpRequest();
                              xhr.open(options.method || 'GET', options.url, true);

                              if (options.headers) {
                                  Object.keys(options.headers).forEach(key => {
                                      xhr.setRequestHeader(key, options.headers[key]);
                                  });
                              }

                              xhr.setRequestHeader('X-Xiaomeihua-License', window.xiaomeihuaLicenseKey || '');

                              xhr.onload = function() {
                                  if (typeof options.onload === 'function') {
                                      options.onload({
                                          status: xhr.status,
                                          statusText: xhr.statusText,
                                          responseText: xhr.responseText,
                                          responseHeaders: xhr.getAllResponseHeaders(),
                                          response: xhr.response,
                                          finalUrl: options.url
                                      });
                                  }
                              };

                              xhr.onerror = function() {
                                  if (typeof options.onerror === 'function') {
                                      options.onerror(new Error('Network error'));
                                  }
                              };

                              xhr.send(options.data);

                              return {
                                  abort: function() {
                                      xhr.abort();
                                  }
                              };
                          };
                      }

                      console.log('✅ Tampermonkey API设置完成');
                  },

                  // 设置API
                  setupAPI: function() {
                      // 创建与后台的通信接口
                      window.aiKnowledgeAPI = {
                          // 获取知识库数据
                          getKnowledgeData: function() {
                              return fetch('/api/knowledge.php', {
                                  method: 'GET',
                                  headers: {
                                      'Content-Type': 'application/json',
                                      'X-Xiaomeihua-License': window.xiaomeihuaLicenseKey || ''
                                  }
                              }).then(response => response.json());
                          },

                          // 保存知识库数据
                          saveKnowledgeData: function(data) {
                              return fetch('/api/knowledge.php', {
                                  method: 'POST',
                                  headers: {
                                      'Content-Type': 'application/json',
                                      'X-Xiaomeihua-License': window.xiaomeihuaLicenseKey || ''
                                  },
                                  body: JSON.stringify(data)
                              }).then(response => response.json());
                          },

                          // AI对话接口
                          chatWithAI: function(message) {
                              return fetch('/api/ai-chat.php', {
                                  method: 'POST',
                                  headers: {
                                      'Content-Type': 'application/json',
                                      'X-Xiaomeihua-License': window.xiaomeihuaLicenseKey || ''
                                  },
                                  body: JSON.stringify({ message: message })
                              }).then(response => response.json());
                          }
                      };
                  },

                  // 绑定事件
                  bindEvents: function() {
                      // 监听来自APP的消息
                      window.addEventListener('message', (event) => {
                          if (event.data && event.data.type === 'ai-knowledge-command') {
                              this.handleCommand(event.data.command, event.data.data);
                          }
                      });

                      // 监听键盘快捷键
                      document.addEventListener('keydown', (event) => {
                          // Ctrl+Shift+K 打开AI知识库
                          if (event.ctrlKey && event.shiftKey && event.key === 'K') {
                              event.preventDefault();
                              this.showKnowledgePanel();
                          }
                      });
                  },

                  // 处理命令
                  handleCommand: function(command, data) {
                      switch (command) {
                          case 'show-panel':
                              this.showKnowledgePanel();
                              break;
                          case 'hide-panel':
                              this.hideKnowledgePanel();
                              break;
                          case 'load-knowledge':
                              this.loadKnowledge(data);
                              break;
                          default:
                              console.log('未知命令:', command);
                      }
                  },

                  // 显示知识库面板
                  showKnowledgePanel: function() {
                      // 这里可以实现知识库面板的显示逻辑
                      console.log('显示AI知识库面板');
                  },

                  // 隐藏知识库面板
                  hideKnowledgePanel: function() {
                      console.log('隐藏AI知识库面板');
                  },

                  // 加载知识库
                  loadKnowledge: function(data) {
                      console.log('加载知识库数据:', data);
                  }
              };

              // 初始化AI知识库
              window.xiaomeihuaAIKnowledge.init();

              // 通知APP已准备就绪
              if (window.parent !== window) {
                  window.parent.postMessage({
                      type: 'ai-knowledge-ready',
                      data: { version: '1.0.0' }
                  }, '*');
              }
          }

          // 注入通用AI助手
          function injectGeneralAIAssistant() {
              // 检查是否已经初始化
              if (window.xiaomeihuaAI) {
                  console.log('AI助手已经初始化');
                  return;
              }

              // 创建通用AI助手（简化版）
              window.xiaomeihuaAI = {
                  version: '1.0.0',
                  initialized: true,

                  init: function() {
                      console.log('通用AI助手初始化完成');
                  }
              };

              window.xiaomeihuaAI.init();
          }
      })();
    `;

    console.log('成功生成AI知识库脚本');
    return aiKnowledgeScript;
  } catch (error) {
    console.error('生成AI知识库脚本失败:', error);
    return null;
  }
}

// 创建注入脚本
function createInjectionScript(licenseKey, shopInfo) {
  // 尝试获取完整的tampermonkey浏览器脚本
  const tampermonkeyScript = getTampermonkeyScript();
  
  if (tampermonkeyScript) {
    console.log('使用完整的tampermonkey浏览器脚本');
    
    // 提取需要传递的店铺信息
    const shopData = shopInfo || {};
    const shopName = shopData.shopName || '未知店铺';
    const shopId = shopData.shopId || '';
    const wechatStoreId = shopData.wechatStoreId || '';
    
    // 创建API模拟脚本
    const apiScript = `
      // 设置全局卡密变量
      window.xiaomeihuaLicenseKey = "${licenseKey}";
      
      // 设置店铺信息
      window.xiaomeihuaShopInfo = {
        shopName: "${shopName}",
        shopId: "${shopId}",
        wechatStoreId: "${wechatStoreId}"
      };
      
      // 创建API配置对象
      window.API_CONFIG = {
        endpoints: {
          script: '/api/verify.php',
          verify: '/api/verify.php',
          data: '/api/data.php'
        },
        baseUrl: 'https://xiaomeihuakefu.cn',
        timeout: 10000,
        retries: 3
      };
      
      // 创建全局API存储对象
      window._tampermonkeyAPIs = {};
      
      // 完整模拟Tampermonkey API
      window.GM_setValue = function(key, value) {
        localStorage.setItem('xiaomeihua_' + key, JSON.stringify(value));
        window._tampermonkeyAPIs.GM_setValue = window.GM_setValue;
        return true;
      };
      
      window.GM_getValue = function(key, defaultValue) {
        const value = localStorage.getItem('xiaomeihua_' + key);
        window._tampermonkeyAPIs.GM_getValue = window.GM_getValue;
        return value !== null ? JSON.parse(value) : defaultValue;
      };
      
      window.GM_deleteValue = function(key) {
        localStorage.removeItem('xiaomeihua_' + key);
        window._tampermonkeyAPIs.GM_deleteValue = window.GM_deleteValue;
        return true;
      };
      
      window.GM_xmlhttpRequest = function(options) {
        const xhr = new XMLHttpRequest();
        xhr.open(options.method || 'GET', options.url, true);
        
        if (options.headers) {
          Object.keys(options.headers).forEach(key => {
            xhr.setRequestHeader(key, options.headers[key]);
          });
        }
        
        xhr.setRequestHeader('X-Xiaomeihua-License', "${licenseKey}");
        
        xhr.onload = function() {
          if (typeof options.onload === 'function') {
            options.onload({
              status: xhr.status,
              statusText: xhr.statusText,
              responseText: xhr.responseText,
              responseHeaders: xhr.getAllResponseHeaders(),
              response: xhr.response,
              finalUrl: options.url
            });
          }
        };
        
        xhr.onerror = function() {
          if (typeof options.onerror === 'function') {
            options.onerror(new Error('Network error'));
          }
        };
        
        xhr.send(options.data);
        
        window._tampermonkeyAPIs.GM_xmlhttpRequest = window.GM_xmlhttpRequest;
        
        return {
          abort: function() {
            xhr.abort();
          }
        };
      };
      
      window.GM_addStyle = function(css) {
        const style = document.createElement('style');
        style.textContent = css;
        document.head.appendChild(style);
        window._tampermonkeyAPIs.GM_addStyle = window.GM_addStyle;
        return style;
      };
      
      window.GM_openInTab = function(url, options) {
        window._tampermonkeyAPIs.GM_openInTab = window.GM_openInTab;
        window.open(url, '_blank');
        return null;
      };
      
      window.unsafeWindow = window;
      
      // 初始化店铺信息到本地存储
      window.GM_setValue('saved_license_key_xiaomeihua', "${licenseKey}");
      
      console.log('小梅花AI脚本API设置完成');
      
      // 【新增】添加直接执行函数
      window.xiaomeihuaForceInit = function() {
        console.log('强制初始化小梅花脚本...');
        
        // 检查是否有main函数
        if (typeof window.main === 'function') {
          console.log('调用main函数...');
          window.main();
        } else {
          console.log('未找到main函数');
        }
        
        // 检查是否有createFloatingIcon函数
        if (typeof window.createFloatingIcon === 'function') {
          console.log('调用createFloatingIcon函数...');
          window.createFloatingIcon();
        }
        
        // 检查是否有createControlPanel函数
        if (typeof window.createControlPanel === 'function') {
          console.log('调用createControlPanel函数...');
          window.createControlPanel();
        }
        
        // 检查是否有restorePanelState函数
        if (typeof window.restorePanelState === 'function') {
          console.log('调用restorePanelState函数...');
          window.restorePanelState();
        }
        
        return '强制初始化完成';
      };
      
      // 【新增】添加强制显示浮动图标的函数
      window.forceShowFloatingIcon = function() {
        console.log('强制显示浮动图标...');
        
        // 检查浮动图标是否存在
        let icon = document.getElementById('floating-icon');
        
        if (!icon) {
          console.log('浮动图标不存在，尝试创建...');
          
          // 尝试创建浮动图标
          if (typeof window.createFloatingIcon === 'function') {
            icon = window.createFloatingIcon();
          } else {
            console.log('无法创建浮动图标：createFloatingIcon函数不存在');
            return '无法创建浮动图标';
          }
        }
        
        // 强制设置样式确保可见
        icon.style.cssText = \`
          position: fixed !important;
          top: 20px;
          left: 20px;
          width: 60px;
          height: 60px;
          border-radius: 50%;
          cursor: pointer;
          z-index: 2147483647 !important;
          display: block !important;
          opacity: 1 !important;
          transform: scale(1);
          background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500, #e55039, #3c6382);
          background-size: 300% 300%;
          visibility: visible !important;
          pointer-events: auto !important;
        \`;
        
        // 确保图标在DOM中
        if (!document.body.contains(icon)) {
          document.body.appendChild(icon);
        }
        
        // 重新添加到body末尾确保在最上层
        document.body.appendChild(icon);
        
        // 添加样式修复
        const style = document.createElement('style');
        style.textContent = \`
          #floating-icon {
            position: fixed !important;
            z-index: 2147483647 !important;
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            pointer-events: auto !important;
          }
        \`;
        document.head.appendChild(style);
        
        return '浮动图标已强制显示';
      };
      
      // 【新增】页面加载完成后自动执行
      window.addEventListener('load', function() {
        console.log('页面加载完成，准备执行脚本...');
        setTimeout(function() {
          // 注入AI知识库脚本
          ${getAIKnowledgeScript()}

          // 注入完整的tampermonkey浏览器脚本
          ${tampermonkeyScript}

          // 延迟执行初始化
          setTimeout(function() {
            if (typeof window.main === 'function') {
              console.log('页面加载后执行main函数...');
              window.main();
            }

            // 再次延迟检查UI是否显示
            setTimeout(function() {
              if (!document.getElementById('floating-icon')) {
                console.log('未检测到浮动图标，强制初始化...');
                window.xiaomeihuaForceInit();
              }

              // 强制显示浮动图标
              window.forceShowFloatingIcon();

              // 设置定时器，每隔一段时间检查并强制显示
              setInterval(window.forceShowFloatingIcon, 5000);
            }, 3000);
          }, 1000);
        }, 500);
      });
    `;
    
    return apiScript;
  }
  
  // 如果没有找到tampermonkey浏览器脚本，回退到使用kamiyanScript
  const kamiyanScript = getKamiyanScript();
  const xinkamiScript = getXinkamiScript();
  
  if (!kamiyanScript && !xinkamiScript) {
    console.error('无法获取任何脚本');
    return null;
  }
  
  // 提取需要传递的店铺信息
  const shopData = shopInfo || {};
  const shopName = shopData.shopName || '未知店铺';
  const shopId = shopData.shopId || '';
  const wechatStoreId = shopData.wechatStoreId || '';
  const expireDate = shopData.expireDate || '';
  const hasCustomerService = shopData.hasCustomerService || false;
  const hasProductListing = shopData.hasProductListing || false;
  const functionType = shopData.functionType || '';
  
  console.log('创建注入脚本，卡密:', licenseKey.substring(0, 8) + '...');
  console.log('店铺信息:', {shopName, shopId, wechatStoreId, functionType});
  
  // 创建注入脚本
  return `
    // 设置全局卡密变量
    window.xiaomeihuaLicenseKey = "${licenseKey}";
    
    // 设置店铺信息
    window.xiaomeihuaShopInfo = {
      shopName: "${shopName}",
      shopId: "${shopId}",
      wechatStoreId: "${wechatStoreId}",
      expireDate: "${expireDate}",
      hasCustomerService: ${hasCustomerService},
      hasProductListing: ${hasProductListing},
      functionType: "${functionType}"
    };
    
    // 创建API配置对象
    window.API_CONFIG = {
      endpoints: {
        script: '/api/verify.php',
        verify: '/api/verify.php',
        data: '/api/data.php'
      },
      baseUrl: 'https://xiaomeihuakefu.cn',
      timeout: 10000,
      retries: 3
    };
    
    // 创建全局API存储对象
    window._tampermonkeyAPIs = {};
    
    // 完整模拟Tampermonkey API
    
    // 存储API
    window.GM_setValue = function(key, value) {
      console.log('GM_setValue:', key);
      localStorage.setItem('xiaomeihua_' + key, JSON.stringify(value));
      window._tampermonkeyAPIs.GM_setValue = window.GM_setValue;
      return true;
    };
    
    window.GM_getValue = function(key, defaultValue) {
      console.log('GM_getValue:', key);
      const value = localStorage.getItem('xiaomeihua_' + key);
      window._tampermonkeyAPIs.GM_getValue = window.GM_getValue;
      return value !== null ? JSON.parse(value) : defaultValue;
    };
    
    window.GM_deleteValue = function(key) {
      console.log('GM_deleteValue:', key);
      localStorage.removeItem('xiaomeihua_' + key);
      window._tampermonkeyAPIs.GM_deleteValue = window.GM_deleteValue;
      return true;
    };
    
    window.GM_listValues = function() {
      console.log('GM_listValues');
      const keys = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key.startsWith('xiaomeihua_')) {
          keys.push(key.substring(11));
        }
      }
      window._tampermonkeyAPIs.GM_listValues = window.GM_listValues;
      return keys;
    };
    
    // 网络请求API
    window.GM_xmlhttpRequest = function(options) {
      console.log('GM_xmlhttpRequest:', options.url);
      
      // 修正URL，如果是访问xinkami.js，则改为verify.php
      if (options.url && options.url.includes('/api/xinkami.js')) {
        console.log('检测到xinkami.js请求，修正为verify.php');
        options.url = options.url.replace('/api/xinkami.js', '/api/verify.php');
      }
      
      const xhr = new XMLHttpRequest();
      xhr.open(options.method || 'GET', options.url, true);
      
      if (options.headers) {
        Object.keys(options.headers).forEach(key => {
          xhr.setRequestHeader(key, options.headers[key]);
        });
      }
      
      // 添加卡密和店铺信息到请求头
      xhr.setRequestHeader('X-Xiaomeihua-License', "${licenseKey}");
      xhr.setRequestHeader('X-Xiaomeihua-Shop-ID', "${shopId}");
      xhr.setRequestHeader('X-Xiaomeihua-Wechat-Store-ID', "${wechatStoreId}");
      xhr.setRequestHeader('X-Xiaomeihua-Function-Type', "${functionType}");
      
      if (options.timeout) {
        xhr.timeout = options.timeout;
      }
      
      xhr.onload = function() {
        if (typeof options.onload === 'function') {
          options.onload({
            status: xhr.status,
            statusText: xhr.statusText,
            responseText: xhr.responseText,
            responseHeaders: xhr.getAllResponseHeaders(),
            response: xhr.response,
            responseXML: xhr.responseXML,
            readyState: xhr.readyState,
            finalUrl: options.url
          });
        }
      };
      
      xhr.onerror = function() {
        if (typeof options.onerror === 'function') {
          options.onerror(new Error('Network error'));
        }
      };
      
      xhr.ontimeout = function() {
        if (typeof options.ontimeout === 'function') {
          options.ontimeout(new Error('Request timed out'));
        }
      };
      
      xhr.onabort = function() {
        if (typeof options.onabort === 'function') {
          options.onabort(new Error('Request aborted'));
        }
      };
      
      xhr.onreadystatechange = function() {
        if (typeof options.onreadystatechange === 'function') {
          options.onreadystatechange(xhr);
        }
      };
      
      xhr.send(options.data);
      
      window._tampermonkeyAPIs.GM_xmlhttpRequest = window.GM_xmlhttpRequest;
      
      return {
        abort: function() {
          xhr.abort();
        }
      };
    };
    
    // UI相关API
    window.GM_addStyle = function(css) {
      console.log('GM_addStyle');
      const style = document.createElement('style');
      style.textContent = css;
      document.head.appendChild(style);
      window._tampermonkeyAPIs.GM_addStyle = window.GM_addStyle;
      return style;
    };
    
    // 浏览器交互API
    window.GM_openInTab = function(url, options) {
      console.log('GM_openInTab:', url);
      window._tampermonkeyAPIs.GM_openInTab = window.GM_openInTab;
      window.open(url, '_blank');
      return null;
    };
    
    window.unsafeWindow = window;
    
    // 初始化店铺信息到本地存储
    window.GM_setValue('saved_license_key_xiaomeihua', "${licenseKey}");
    
    console.log('小梅花AI脚本API设置完成');
    
    // 【新增】添加直接执行函数
    window.xiaomeihuaForceInit = function() {
      console.log('强制初始化小梅花脚本...');
      
      // 检查是否有main函数
      if (typeof window.main === 'function') {
        console.log('调用main函数...');
        window.main();
      }
      
      // 延迟执行其他初始化函数
      setTimeout(function() {
        // 检查是否有createFloatingIcon函数
        if (typeof window.createFloatingIcon === 'function') {
          console.log('调用createFloatingIcon函数...');
          window.createFloatingIcon();
        }
        
        // 检查是否有createControlPanel函数
        if (typeof window.createControlPanel === 'function') {
          console.log('调用createControlPanel函数...');
          window.createControlPanel();
        }
        
        // 检查是否有restorePanelState函数
        if (typeof window.restorePanelState === 'function') {
          console.log('调用restorePanelState函数...');
          window.restorePanelState();
        }
      }, 1000);
      
      return '强制初始化完成';
    };
    
    // 【新增】添加强制显示浮动图标的函数
    window.forceShowFloatingIcon = function() {
      console.log('强制显示浮动图标...');
      
      // 检查浮动图标是否存在
      let icon = document.getElementById('floating-icon');
      
      if (!icon) {
        console.log('浮动图标不存在，尝试创建...');
        
        // 尝试创建浮动图标
        if (typeof window.createFloatingIcon === 'function') {
          icon = window.createFloatingIcon();
        } else {
          console.log('无法创建浮动图标：createFloatingIcon函数不存在');
          return '无法创建浮动图标';
        }
      }
      
      // 强制设置样式确保可见
      icon.style.cssText = \`
        position: fixed !important;
        top: 20px;
        left: 20px;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        cursor: pointer;
        z-index: 2147483647 !important;
        display: block !important;
        opacity: 1 !important;
        transform: scale(1);
        background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500, #e55039, #3c6382);
        background-size: 300% 300%;
        visibility: visible !important;
        pointer-events: auto !important;
      \`;
      
      // 确保图标在DOM中
      if (!document.body.contains(icon)) {
        document.body.appendChild(icon);
      }
      
      // 重新添加到body末尾确保在最上层
      document.body.appendChild(icon);
      
      // 添加样式修复
      const style = document.createElement('style');
      style.textContent = \`
        #floating-icon {
          position: fixed !important;
          z-index: 2147483647 !important;
          display: block !important;
          opacity: 1 !important;
          visibility: visible !important;
          pointer-events: auto !important;
        }
      \`;
      document.head.appendChild(style);
      
      return '浮动图标已强制显示';
    };
    
    // 【新增】页面加载完成后自动执行
    window.addEventListener('load', function() {
      console.log('页面加载完成，准备执行脚本...');
      setTimeout(function() {
        // 注入脚本
        ${kamiyanScript || ''}
        ${xinkamiScript || ''}
        
        // 延迟执行初始化
        setTimeout(function() {
          if (typeof window.main === 'function') {
            console.log('页面加载后执行main函数...');
            window.main();
          }
          
          // 再次延迟检查UI是否显示
          setTimeout(function() {
            if (!document.getElementById('floating-icon')) {
              console.log('未检测到浮动图标，强制初始化...');
              window.xiaomeihuaForceInit();
            }
            
            // 强制显示浮动图标
            window.forceShowFloatingIcon();
            
            // 设置定时器，每隔一段时间检查并强制显示
            setInterval(window.forceShowFloatingIcon, 5000);
          }, 3000);
        }, 1000);
      }, 500);
    });
  `;
}

module.exports = {
  getKamiyanScript,
  getXinkamiScript,
  getTampermonkeyScript,
  getAIKnowledgeScript,
  createInjectionScript
};