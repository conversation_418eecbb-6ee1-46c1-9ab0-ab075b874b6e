const { app, BrowserWindow, ipcMain, dialog, shell, session, Menu, BrowserView } = require('electron');
const path = require('path');
const fs = require('fs');
const axios = require('axios');
const Store = require('electron-store');
const { v4: uuidv4 } = require('uuid');
const { createInjectionScript } = require('./kamiyan-injector');
const cookieManager = require('./cookie-manager'); // 导入Cookie管理器
const WindowsLoginManager = require('./windows-login-manager'); // 导入Windows登录管理器
const WindowsPersistentStorage = require('./windows-persistent-storage'); // 导入Windows专用存储
const DesktopBackupCleaner = require('./cleanup-desktop-backup'); // 导入桌面备份清理器

// 新增：导入APP设置相关模块
const AppSettingsAPI = require('./app-settings-api');
const PopupManager = require('./popup-manager');
const AgreementManager = require('./agreement-manager');

// 新增：导入自动更新模块
const AppUpdater = require('./app-updater');

// 新增：导入版本管理器
const versionManager = require('./version-manager');

// 新增：导入架构检测器
const architectureDetector = require('./architecture-detector');


// 存储配置
const store = new Store({
  name: 'xiaomeihua-config',
  encryptionKey: 'xiaomeihua-secure-key-2025',
  clearInvalidConfig: true,
  serialize: (value) => JSON.stringify(value, null, 2),
  deserialize: (value) => {
    try {
      return JSON.parse(value);
    } catch (error) {
      console.error('配置文件解析错误:', error);
      return {};
    }
  }
});

// 会话数据文件路径
const SESSION_DATA_PATH = path.join(app.getPath('userData'), 'session-data.json');
const COOKIES_DATA_PATH = path.join(app.getPath('userData'), 'cookies-data.json');
const LOCAL_STORAGE_PATH = path.join(app.getPath('userData'), 'local-storage-data.json');

// 全局变量
let mainWindow = null;
let loginWindow = null;
let aboutWindow = null; // 新增：关于窗口
let shopWindows = {};
let isQuitting = false;
let windowsLoginManager = null; // Windows登录管理器
let windowsPersistentStorage = null; // Windows专用存储

// 新增：APP设置相关管理器
let appSettingsAPI = null;
let popupManager = null;
let agreementManager = null;

// 新增：自动更新管理器
let appUpdater = null;

// 服务器地址配置
const SERVER_CONFIG = {
  main: 'https://xiaomeihuakefu.cn',
  backup: 'https://api.xiaomeihuakefu.cn',
  secure: 'https://secure.xiaomeihuakefu.cn',
  timeout: 8000, // 减少超时时间到8秒
  retries: 1     // 减少重试次数到1次
};

// 添加网络请求工具函数
async function makeRequest(url, data, options = {}) {
  const { timeout = SERVER_CONFIG.timeout, retries = SERVER_CONFIG.retries } = options;
  let lastError = null;
  
  for (let i = 0; i <= retries; i++) {
    try {
      console.log(`尝试请求 ${url} (尝试 ${i+1}/${retries+1})`);
      
      // 判断data是否为对象，如果是则转换为URL编码格式
      let postData = data;
      let headers = { 'Content-Type': 'application/x-www-form-urlencoded' };
      
      if (typeof data === 'object' && data !== null) {
        // 将对象转换为URL编码格式
        const params = new URLSearchParams();
        for (const key in data) {
          params.append(key, data[key]);
        }
        postData = params.toString();
      }
      
      console.log('请求数据:', postData);
      const response = await axios.post(url, postData, { 
        headers, 
        timeout,
        maxContentLength: 10 * 1024 * 1024, // 10MB
        maxBodyLength: 10 * 1024 * 1024 // 10MB
      });
      return response;
    } catch (error) {
      console.error(`请求 ${url} 失败 (尝试 ${i+1}/${retries+1}):`, error.message);
      lastError = error;
      
      // 如果不是最后一次尝试，等待一段时间后重试
      if (i < retries) {
        const delay = 500; // 减少延迟到500ms
        console.log(`等待 ${delay}ms 后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  // 所有尝试都失败了
  throw lastError;
}

// 允许访问的域名
const ALLOWED_DOMAINS = [
  'xiaomeihuakefu.cn',
  'api.xiaomeihuakefu.cn',
  'secure.xiaomeihuakefu.cn',
  'store.weixin.qq.com',
  'filehelper.weixin.qq.com',
  'channels.weixin.qq.com',
  'weixin.qq.com',
  'wx.qq.com',
  'mp.weixin.qq.com'
];

// 全局错误处理
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);

  // 【修复】忽略"Object has been destroyed"错误，这通常在窗口关闭时发生
  if (error.message && error.message.includes('Object has been destroyed')) {
    console.log('忽略"Object has been destroyed"错误，这是正常的窗口关闭行为');
    return;
  }

  // 如果已经有窗口打开，显示错误对话框
  if (BrowserWindow.getAllWindows().length > 0) {
    dialog.showErrorBox(
      '应用程序错误',
      `发生了一个错误: ${error.message}\n\n请重启应用程序。`
    );
  }
});

// 【新增】处理未处理的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);

  // 【修复】忽略"Object has been destroyed"错误
  if (reason && reason.message && reason.message.includes('Object has been destroyed')) {
    console.log('忽略Promise中的"Object has been destroyed"错误');
    return;
  }

  // 记录详细信息但不显示错误对话框，避免干扰用户体验
  console.error('Promise:', promise);
});

// 应用启动
app.whenReady().then(async () => {
  try {
    // 设置应用名称（使用英文名称避免路径问题）
    app.setName('xiaomeihua-ai');

    // 基于参考代码的成功方法：立即初始化Cookie管理器
    console.log('🔄 初始化Cookie管理器（基于成功参考代码）...');
    await cookieManager.initialize();
    console.log('✅ Cookie管理器初始化完成（包含Cookie恢复）');

    // 【Windows平台优化】初始化Windows专用存储和登录管理器
    if (process.platform === 'win32') {
      // 清理桌面备份文件夹（优化：移除桌面上的旧备份文件夹）
      try {
        const desktopCleaner = new DesktopBackupCleaner();
        await desktopCleaner.performCleanup();
        console.log('✅ 桌面备份文件夹清理完成');
      } catch (error) {
        console.warn('⚠️ 桌面备份文件夹清理失败，但不影响应用运行:', error.message);
      }

      // 初始化专用存储系统
      windowsPersistentStorage = new WindowsPersistentStorage();
      await windowsPersistentStorage.initialize();
      console.log('✅ Windows专用存储系统初始化完成');

      // 初始化登录管理器
      windowsLoginManager = new WindowsLoginManager();
      await windowsLoginManager.initialize();
      console.log('✅ Windows登录管理器初始化完成');
    }

    // 【关键修复】应用启动时初始化微信小店状态（包含状态检查和恢复）
    try {
      // 先进行状态检查和清理，再决定是否恢复
      await cookieManager.initializeWeixinStoreOnStartup();
      console.log('✅ 微信小店启动状态初始化完成');
    } catch (error) {
      console.error('❌ 微信小店启动状态初始化失败:', error);
    }

    // 新增：初始化APP设置相关模块
    try {
      console.log('🔄 初始化APP设置模块...');
      
      // 初始化API客户端
      appSettingsAPI = new AppSettingsAPI();
      console.log('✅ APP设置API客户端初始化完成');
      
      // 初始化协议管理器
      agreementManager = new AgreementManager();
      console.log('✅ 协议管理器初始化完成');
      
      // 初始化弹窗管理器
      popupManager = new PopupManager();
      global.popupManager = popupManager; // 设置为全局变量，供其他模块访问
      console.log('✅ 弹窗管理器初始化完成');

      // 新增：初始化自动更新管理器
      appUpdater = new AppUpdater();  
      console.log('✅ 自动更新管理器初始化完成');

      // 检查更新（优先于登录窗口显示）
      const hasUpdate = await appUpdater.checkForUpdates();

      if (hasUpdate) {
        // 如果有更新，更新弹窗会自动显示，不显示登录窗口，也不启动弹窗管理器
        console.log('✅ 发现更新，显示更新弹窗，暂停弹窗管理器');
        // 弹窗管理器将在更新完成后启动
      } else {
        // 没有更新，启动弹窗管理器并显示登录窗口
        // 启动弹窗管理器（会定期检查弹窗）
        // 不清除历史记录，保持弹窗频率控制
        popupManager.start({ clearHistory: false });
        console.log('✅ 弹窗管理器已启动（保持历史记录）');

        // 正常显示登录窗口
        createLoginWindow();
      }

    } catch (error) {
      console.error('❌ APP设置模块初始化失败:', error);
      // 即使更新检查失败，也要创建登录窗口，确保应用可以正常使用
      console.log('⚠️ 更新检查失败，创建登录窗口以确保应用可用');
      createLoginWindow();
    }

    // 【增强】检查是否需要清理微信小店状态
    try {
      const weixinPagesActiveOnExit = store.get('weixin_pages_active_on_exit');
      const exitTimestamp = store.get('exit_timestamp');
      const logoutDetected = store.get('logout_detected');
      const pageExited = store.get('page_exited');
      const currentTime = Date.now();

      console.log('🔍 启动时状态检查:', {
        weixinPagesActiveOnExit,
        exitTimestamp,
        logoutDetected,
        pageExited,
        currentTime
      });

      // 【关键修复】如果检测到退出登录或页面退出，立即清理状态
      if (logoutDetected || pageExited) {
        console.log('🔄 检测到退出登录标记，执行完整清理确保可以重新扫码登录...');
        await cookieManager.resetWeixinStoreLoginState();
        await cookieManager.clearWeixinStoreSession();

        // 清理所有相关标记
        store.delete('logout_detected');
        store.delete('page_exited');
        store.delete('page_exit_timestamp');
        store.delete('page_exit_event');
        store.delete('manual_logout');
        store.delete('logout_timestamp');

        console.log('✅ 启动时退出登录状态清理完成，确保可以重新扫码登录');
      }
      // 如果上次退出时有微信页面活跃，且时间在10分钟内，清理状态
      else if (weixinPagesActiveOnExit && exitTimestamp && (currentTime - exitTimestamp) < 600000) {
        console.log('🔄 检测到上次退出时有微信小店页面活跃，清理状态...');
        await cookieManager.clearWeixinStoreSession();
        store.delete('weixin_pages_active_on_exit');
        store.delete('exit_timestamp');
        console.log('✅ 启动时微信小店状态清理完成');
      }
    } catch (error) {
      console.error('❌ 启动时清理微信小店状态失败:', error);
    }

    // 【优化】将登录窗口创建移到更新检查完成后

    // 异步执行其他初始化任务
    Promise.all([
      setupSessionPersistence(),
      setupExtensions()
    ]).catch(error => {
      console.error('初始化任务失败:', error);
      // 即使初始化失败也不影响基本功能
    });

    // 注册IPC事件处理
    setupIpcHandlers();

    // Cookie管理器会自动处理登录状态变化

    // 立即保存一次会话状态
    setTimeout(() => {
      cookieManager.forceSaveCookies();
    }, 5000);

    // MacOS应用激活处理
    app.on('activate', async () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        createLoginWindow();
      }
    });
  } catch (error) {
    console.error('应用启动错误:', error);
    dialog.showErrorBox(
      '启动错误',
      `应用程序启动失败: ${error.message}\n\n请重启应用程序。`
    );
  }
});

// 应用退出处理
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('before-quit', async (event) => {
  // 如果正在保存Cookie，阻止退出
  if (!isQuitting) {
    event.preventDefault();
    isQuitting = true;

    console.log('应用程序即将退出，保存登录状态...');

    try {
      // 【新增】记录应用关闭时间
      store.set('last_shutdown_time', Date.now());

      // 新增：清理APP设置相关模块
      try {
        console.log('🔄 清理APP设置模块...');
        
        if (popupManager) {
          popupManager.stop();
          console.log('✅ 弹窗管理器已停止');
        }

        if (appUpdater) {
          appUpdater.closeUpdateWindow();
          console.log('✅ 自动更新管理器已清理');
        }
        
        console.log('✅ APP设置模块清理完成');
      } catch (error) {
        console.error('❌ APP设置模块清理失败:', error);
      }

      // 【新增】检查是否需要清理微信小店状态
      try {
        // 检查是否有活跃的微信小店页面
        const windows = BrowserWindow.getAllWindows();
        let hasWeixinPages = false;

        for (const win of windows) {
          if (!win.isDestroyed() && win.webContents) {
            const url = win.webContents.getURL();
            if (url && (url.includes('store.weixin.qq.com') || url.includes('weixin.qq.com'))) {
              hasWeixinPages = true;
              break;
            }
          }
        }

        // 如果有微信页面，标记需要在下次启动时清理状态
        if (hasWeixinPages) {
          console.log('🔄 检测到微信小店页面，标记需要清理状态...');
          store.set('weixin_pages_active_on_exit', true);
          store.set('exit_timestamp', Date.now());
        }

        // 【优化】根据退出登录状态决定是否保存登录状态
        const logoutDetected = store.get('logout_detected');

        if (logoutDetected) {
          console.log('🚪 检测到用户退出登录，不保存登录状态');
          // 用户已退出登录，不保存登录状态，只清理冲突状态
          await cookieManager.clearWeixinConflictState();
          console.log('✅ 应用退出时微信小店冲突状态清理完成');
        } else {
          console.log('💾 用户未退出登录，保存登录状态');
          // 用户未退出登录，保存登录状态
          await cookieManager.saveWeixinLoginState();
          console.log('✅ 应用退出时微信小店登录状态保存完成');

          // 只清理冲突状态，不清理登录状态
          await cookieManager.clearWeixinConflictState();
          console.log('✅ 应用退出时微信小店冲突状态清理完成');
        }
      } catch (error) {
        console.error('❌ 应用退出时清理微信小店状态失败:', error);
      }

      // 保存所有Cookie确保登录状态不丢失
      console.log('🔄 应用退出前保存Cookie...');
      await cookieManager.forceSaveCookies();
      console.log('✅ 应用退出前Cookie保存完成');

      // 【Windows平台优化】使用专用存储系统保存状态
      if (process.platform === 'win32' && windowsPersistentStorage) {
        console.log('🔄 Windows平台专用存储系统保存登录状态...');
        try {
          // 收集完整登录数据
          const loginData = {
            cookies: await cookieManager.getAllCookies(),
            timestamp: Date.now(),
            sessionData: await getSessionDataForWindows(),
            localStorage: await getLocalStorageDataForWindows(),
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            platform: 'win32'
          };

          // 使用专用存储系统保存（多重备份）
          await windowsPersistentStorage.saveLoginState(loginData);

          // 使用传统登录管理器作为额外备份
          if (windowsLoginManager) {
            await windowsLoginManager.saveLoginState(loginData);
          }

          // 传统方式保存作为最后备份
          await cookieManager.saveCompleteLoginState();

          console.log('✅ Windows平台多重登录状态保存完成');
        } catch (winError) {
          console.error('❌ Windows平台状态保存失败:', winError);
        }
      }

      // 4. 保存会话状态
      await saveSessionState();
      console.log('保存会话状态完成');
      
      // 5. 额外的备份保存
      setTimeout(async () => {
        try {
          await cookieManager.forceSaveCookies();
          console.log('备份保存完成，继续退出');
          app.quit();
        } catch (backupError) {
          console.error('备份保存失败:', backupError);
          app.quit();
        }
      }, 500);
      
    } catch (error) {
      console.error('保存登录状态失败:', error);
      // 即使保存失败也要退出，避免应用卡死
      app.quit();
    }
  }
});

// 设置会话持久化
async function setupSessionPersistence() {
  try {
    console.log('设置会话持久化...');
    
    // 配置默认会话
    const defaultSession = session.defaultSession;
    
    // 禁用缓存清理
    defaultSession.clearStorageData = function() {
      console.log('阻止清理存储数据');
      return Promise.resolve();
    };
    
    defaultSession.clearCache = function() {
      console.log('阻止清理缓存');
      return Promise.resolve();
    };
    
    // 配置会话持久化
    const persistConfig = {
      cache: true,
      cookies: true,
      localStorage: true,
      sessionStorage: true,
      webSQL: true,
      indexedDB: true
    };
    
    // 确保会话持久化目录存在
    const sessionDir = path.join(app.getPath('userData'), 'sessions');
    if (!fs.existsSync(sessionDir)) {
      fs.mkdirSync(sessionDir, { recursive: true });

      // 【Windows平台优化】设置会话目录权限
      if (process.platform === 'win32') {
        try {
          const { execSync } = require('child_process');
          const username = require('os').userInfo().username;
          execSync(`icacls "${sessionDir}" /grant "${username}:(OI)(CI)F" /T`, { stdio: 'ignore' });
          console.log('✅ Windows平台会话目录权限设置完成');
        } catch (permError) {
          console.warn('⚠️ Windows平台会话目录权限设置失败:', permError.message);
        }
      }
    }
    
    // 配置请求头
    defaultSession.webRequest.onBeforeSendHeaders((details, callback) => {
      // 修改请求头以防止会话过期
      const requestHeaders = {
        ...details.requestHeaders,
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      };

      // 对微信域名的请求，修改Cookie策略
      if (details.url.includes('weixin.qq.com') ||
          details.url.includes('store.weixin.qq.com') ||
          details.url.includes('filehelper.weixin.qq.com') ||
          details.url.includes('channels.weixin.qq.com') ||
          details.url.includes('wx.qq.com')) {
        requestHeaders['Cookie-Policy'] = 'never-expire';
        requestHeaders['Keep-Alive'] = 'timeout=600, max=1000';
        
        // 记录请求的Cookie
        if (details.requestHeaders.Cookie) {
          console.log(`发送Cookie到 ${new URL(details.url).hostname}: ${details.requestHeaders.Cookie.substring(0, 100)}...`);
          
          // 保存请求中的Cookie
          try {
            const cookieStr = details.requestHeaders.Cookie;
            const cookiePairs = cookieStr.split(';');
            
            // 解析Cookie并保存
            for (const pair of cookiePairs) {
              const trimmedPair = pair.trim();
              if (trimmedPair) {
                const [name, value] = trimmedPair.split('=');
                if (name && value) {
                  // 检查是否是关键Cookie
                  if (name.includes('login') || 
                      name.includes('ticket') || 
                      name.includes('token') || 
                      name.includes('auth') || 
                      name.includes('session') ||
                      name.includes('wxticket')) {
                    console.log(`检测到关键Cookie: ${name}=${value.substring(0, 10)}...`);
                    
                    // 保存到文件
                    const cookieFilePath = path.join(app.getPath('userData'), `cookie-${name}.txt`);
                    fs.writeFileSync(cookieFilePath, `${name}=${value}`);
                    
                    // 立即保存会话状态
                    setTimeout(() => {
                      saveSessionState();
                    }, 100);
                  }
                }
              }
            }
          } catch (err) {
            console.error('处理请求Cookie失败:', err);
          }
        }
      }

      callback({ requestHeaders });
    });
    
    // 配置Cookie设置
    defaultSession.webRequest.onHeadersReceived((details, callback) => {
      if (details.url.includes('weixin.qq.com') ||
          details.url.includes('store.weixin.qq.com') ||
          details.url.includes('filehelper.weixin.qq.com') ||
          details.url.includes('channels.weixin.qq.com') ||
          details.url.includes('wx.qq.com')) {
        // 修改Set-Cookie头，确保Cookie不会过期
        const responseHeaders = {...details.responseHeaders};
        
        // 处理Set-Cookie头
        if (responseHeaders['set-cookie']) {
          const cookies = responseHeaders['set-cookie'];
          console.log(`接收到 ${cookies.length} 个Cookie从 ${new URL(details.url).hostname}`);
          
          const modifiedCookies = cookies.map(cookie => {
            // 记录接收到的Cookie
            console.log(`接收Cookie: ${cookie.split(';')[0]}`);
            
            // 检查是否是关键Cookie
            const cookieName = cookie.split('=')[0];
            if (cookieName && (
                cookieName.includes('login') || 
                cookieName.includes('ticket') || 
                cookieName.includes('token') || 
                cookieName.includes('auth') || 
                cookieName.includes('session') ||
                cookieName.includes('wxticket'))) {
              console.log(`检测到关键Cookie: ${cookie.split(';')[0]}`);
              
              // 保存到文件
              const cookieFilePath = path.join(app.getPath('userData'), `cookie-${cookieName}.txt`);
              fs.writeFileSync(cookieFilePath, cookie.split(';')[0]);
            }
            
            // 移除原有过期时间并设置为10年有效期
            const modifiedCookie = cookie
              .replace(/; expires=[^;]+/gi, '')
              .replace(/; max-age=[^;]+/gi, '')
              .concat('; max-age=315360000'); // 设置为10年
            
            return modifiedCookie;
          });
          
          responseHeaders['set-cookie'] = modifiedCookies;
          console.log('修改了Set-Cookie头，确保Cookie持久化');
          
          // 立即保存会话状态
          setTimeout(() => {
            saveSessionState();
          }, 500);
        }
        
        callback({ responseHeaders });
      } else {
        callback({ responseHeaders: details.responseHeaders });
      }
    });
    
    // 恢复Cookie
    await restoreCookies();
    
    // 关键Cookie已由Cookie管理器自动恢复
    
    // 监听Cookie变化
    defaultSession.cookies.on('changed', async (event, cookie, cause, removed) => {
      // 记录Cookie变化
      if (!removed) {
        console.log(`Cookie变化 (${cause}): ${cookie.name}=${cookie.value.substring(0, 10)}... domain=${cookie.domain}`);

        // 检查是否是关键Cookie
        if (cookie.name.includes('login') ||
            cookie.name.includes('ticket') ||
            cookie.name.includes('token') ||
            cookie.name.includes('auth') ||
            cookie.name.includes('session') ||
            cookie.name.includes('wxticket')) {
          console.log(`检测到关键Cookie变化: ${cookie.name}=${cookie.value.substring(0, 10)}...`);

          // 保存到文件
          const cookieFilePath = path.join(app.getPath('userData'), `cookie-${cookie.name}.txt`);
          fs.writeFileSync(cookieFilePath, `${cookie.name}=${cookie.value}`);

          // 立即通知Cookie管理器保存完整登录状态
          try {
            await cookieManager.saveCompleteLoginState();
            console.log('检测到关键Cookie变化，已保存完整登录状态');
          } catch (err) {
            console.error('保存完整登录状态失败:', err);
          }
        }

        // 如果是微信相关Cookie，立即保存
        if (cookie.domain.includes('weixin.qq.com') ||
            cookie.domain.includes('store.weixin.qq.com') ||
            cookie.domain.includes('filehelper.weixin.qq.com') ||
            cookie.domain.includes('channels.weixin.qq.com') ||
            cookie.domain.includes('wx.qq.com') ||
            cookie.domain.includes('qq.com')) {
          // 立即保存会话状态
          await saveSessionState();

          // 同时通知Cookie管理器检查登录状态
          setTimeout(async () => {
            try {
              await cookieManager.checkAndSaveLoginState();
            } catch (err) {
              console.error('检查登录状态失败:', err);
            }
          }, 1000);
        }
      }
    });
    
    console.log('会话持久化设置完成');
  } catch (error) {
    console.error('设置会话持久化失败:', error);
  }
}

// 恢复单独保存的关键Cookie
async function restoreKeyCookies() {
  try {
    console.log('尝试恢复关键Cookie...');
    
    // 读取userData目录中的所有文件
    const userDataPath = app.getPath('userData');
    const files = fs.readdirSync(userDataPath);
    
    // 过滤出Cookie文件
    const cookieFiles = files.filter(file => file.startsWith('cookie-') && file.endsWith('.txt'));
    
    console.log(`找到 ${cookieFiles.length} 个关键Cookie文件`);
    
    // 恢复每个Cookie
    for (const file of cookieFiles) {
      try {
        const filePath = path.join(userDataPath, file);
        const cookieData = fs.readFileSync(filePath, 'utf-8');
        
        // 解析Cookie
        const [name, value] = cookieData.split('=');
        
        if (name && value) {
          console.log(`从文件恢复关键Cookie: ${name}=${value.substring(0, 10)}...`);
          
          // 设置Cookie到所有相关域名
          for (const domain of ['store.weixin.qq.com', 'filehelper.weixin.qq.com', 'channels.weixin.qq.com', 'weixin.qq.com', 'wx.qq.com']) {
            try {
              await session.defaultSession.cookies.set({
                url: `https://${domain}/`,
                name: name,
                value: value,
                domain: domain,
                path: '/',
                secure: true,
                httpOnly: true,
                expirationDate: Math.floor(Date.now() / 1000) + 86400 * 365, // 一年
                sameSite: 'no_restriction'
              });
              
              console.log(`成功设置Cookie ${name} 到域名 ${domain}`);
            } catch (err) {
              console.error(`设置Cookie ${name} 到域名 ${domain} 失败:`, err);
            }
          }
        }
      } catch (err) {
        console.error(`处理Cookie文件 ${file} 失败:`, err);
      }
    }
    
    console.log('关键Cookie恢复完成');
  } catch (error) {
    console.error('恢复关键Cookie失败:', error);
  }
}

// 保存会话状态
async function saveSessionState() {
  try {
    console.log('保存会话状态...');
    
    // 保存Cookie
    await saveCookies();
    
    // 保存localStorage数据
    // 获取所有窗口
    const windows = BrowserWindow.getAllWindows();
    
    for (const win of windows) {
      try {
        if (win.isDestroyed() || !win.webContents) continue;
        
        const url = win.webContents.getURL();
        
        // 跳过非微信商店页面
        if (!url || (!url.includes('store.weixin.qq.com') && !url.includes('weixin.qq.com'))) {
          continue;
        }
        
        // 获取窗口标题，可能包含店铺ID信息
        const title = win.getTitle() || '';
        
        // 尝试从窗口标题或URL中提取店铺ID
        let shopId = null;
        
        // 从标题中提取
        const titleMatch = title.match(/shopId=([^&\s]+)/i) || title.match(/shop_id=([^&\s]+)/i);
        if (titleMatch && titleMatch[1]) {
          shopId = titleMatch[1];
        }
        
        // 从URL中提取
        if (!shopId) {
          const urlMatch = url.match(/shopId=([^&]+)/i) || url.match(/shop_id=([^&]+)/i);
          if (urlMatch && urlMatch[1]) {
            shopId = urlMatch[1];
          }
        }
        
        // 如果没有找到shopId，尝试使用窗口对象的键
        if (!shopId) {
          for (const [id, window] of Object.entries(shopWindows)) {
            if (window === win) {
              shopId = id;
              break;
            }
          }
        }
        
        // 如果还是没有找到shopId，使用默认值
        shopId = shopId || 'default';
        
        console.log(`从窗口提取的店铺ID: ${shopId}`);
        
        // 获取localStorage数据
        const localStorageData = await win.webContents.executeJavaScript(`
          (function() {
            try {
              const data = {};
              for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                data[key] = localStorage.getItem(key);
              }
              return data;
            } catch (e) {
              return { error: e.toString() };
            }
          })();
        `);
        
        if (localStorageData && !localStorageData.error) {
          // 保存到店铺特定的键
          store.set(`shop-localstorage-${shopId}`, localStorageData);
          console.log(`保存了店铺 ${shopId} 的localStorage数据，共 ${Object.keys(localStorageData).length} 项`);
        }
      } catch (error) {
        console.error('处理窗口时出错:', error);
      }
    }
    
    console.log('会话状态保存完成');
  } catch (error) {
    console.error('保存会话状态失败:', error);
  }
}

// 保存Cookie到文件
async function saveCookies() {
  try {
    // 获取所有Cookie
    const cookies = await session.defaultSession.cookies.get({});
    
    console.log(`获取到 ${cookies.length} 个Cookie`);
    
    // 过滤微信相关的Cookie - 包含所有目标网页域名
    const weixinCookies = cookies.filter(cookie =>
      cookie.domain.includes('weixin.qq.com') ||
      cookie.domain.includes('store.weixin.qq.com') ||
      cookie.domain.includes('filehelper.weixin.qq.com') ||
      cookie.domain.includes('channels.weixin.qq.com') ||
      cookie.domain.includes('wx.qq.com') ||
      cookie.domain.includes('qq.com')
    );
    
    console.log(`保存 ${weixinCookies.length} 个Cookie到文件`);
    
    // 检查是否有关键的登录Cookie
    const hasLoginCookies = weixinCookies.some(cookie => 
      cookie.name.includes('login') || 
      cookie.name.includes('ticket') || 
      cookie.name.includes('token') || 
      cookie.name.includes('auth') || 
      cookie.name.includes('session') ||
      cookie.name.includes('wxticket')
    );
    
    if (!hasLoginCookies) {
      console.warn('警告: 未找到关键的登录Cookie!');
    } else {
      console.log('检测到登录相关Cookie，将保存登录状态');
    }
    
    // 确保每个Cookie都有足够长的过期时间
    const enhancedCookies = weixinCookies.map(cookie => {
      // 设置至少一年的过期时间
      cookie.expirationDate = Math.floor(Date.now() / 1000) + 86400 * 365; // 一年
      
      // 确保httpOnly和secure属性正确设置
      if (cookie.secure === undefined) cookie.secure = true;
      if (cookie.httpOnly === undefined) cookie.httpOnly = true;
      
      // 确保路径属性存在
      if (!cookie.path) cookie.path = '/';
      
      // 记录保存的Cookie详情
      console.log(`保存Cookie: ${cookie.name}=${cookie.value.substring(0, 10)}... (domain: ${cookie.domain})`);
      
      return cookie;
    });
    
    // 写入文件
    fs.writeFileSync(COOKIES_DATA_PATH, JSON.stringify(enhancedCookies, null, 2));
    
    // 同时保存会话数据到独立文件
    try {
      // 为每个域名创建单独的Cookie文件
      const domainGroups = {};
      for (const cookie of enhancedCookies) {
        const domain = cookie.domain.replace(/^\./, ''); // 移除前导点
        if (!domainGroups[domain]) {
          domainGroups[domain] = [];
        }
        domainGroups[domain].push(cookie);
      }
      
      // 保存每个域名的Cookie到单独文件
      for (const [domain, cookies] of Object.entries(domainGroups)) {
        const domainFilePath = path.join(app.getPath('userData'), `cookies-${domain}.json`);
        fs.writeFileSync(domainFilePath, JSON.stringify(cookies, null, 2));
        console.log(`已保存 ${cookies.length} 个Cookie到域名文件: ${domainFilePath}`);
      }
    } catch (err) {
      console.error('保存域名Cookie文件失败:', err);
    }
    
    // 同时保存localStorage数据
    try {
      // 获取所有窗口
      const windows = BrowserWindow.getAllWindows();
      let localStorageData = {};
      
      // 遍历所有窗口，查找微信商店窗口
      for (const win of windows) {
        if (!win.isDestroyed() && win.webContents) {
          const url = win.webContents.getURL();
          
          // 如果是微信相关页面，获取localStorage
          if (url && (url.includes('store.weixin.qq.com') ||
                     url.includes('filehelper.weixin.qq.com') ||
                     url.includes('channels.weixin.qq.com') ||
                     url.includes('weixin.qq.com'))) {
            try {
              const data = await win.webContents.executeJavaScript(`
                (function() {
                  try {
                    const data = {};
                    for (let i = 0; i < localStorage.length; i++) {
                      const key = localStorage.key(i);
                      data[key] = localStorage.getItem(key);
                    }
                    return data;
                  } catch (e) {
                    return { error: e.toString() };
                  }
                })();
              `);
              
              if (data && !data.error) {
                localStorageData = { ...localStorageData, ...data };
              }
              
              // 同时获取document.cookie
              const docCookies = await win.webContents.executeJavaScript(`
                (function() {
                  try {
                    return document.cookie;
                  } catch (e) {
                    return '';
                  }
                })();
              `);
              
              if (docCookies) {
                console.log(`从页面获取到Cookie: ${docCookies.substring(0, 100)}...`);
                
                // 保存document.cookie到文件
                const docCookiesPath = path.join(app.getPath('userData'), 'document-cookies.txt');
                fs.writeFileSync(docCookiesPath, docCookies);
              }
            } catch (err) {
              console.error('获取页面数据失败:', err);
            }
          }
        }
      }
      
      // 写入文件
      fs.writeFileSync(LOCAL_STORAGE_PATH, JSON.stringify(localStorageData, null, 2));
      console.log(`localStorage数据保存完成，共 ${Object.keys(localStorageData).length} 项`);
    } catch (err) {
      console.error('保存localStorage数据失败:', err);
    }
    
    console.log('Cookie保存完成');
  } catch (error) {
    console.error('保存Cookie失败:', error);
  }
}

// 从文件恢复Cookie
async function restoreCookies() {
  try {
    // 先尝试从主Cookie文件恢复
    let mainCookiesRestored = 0;
    if (fs.existsSync(COOKIES_DATA_PATH)) {
      console.log('从主Cookie文件恢复...');
      const cookiesData = fs.readFileSync(COOKIES_DATA_PATH, 'utf-8');
      const cookies = JSON.parse(cookiesData);
      
      console.log(`从主文件恢复 ${cookies.length} 个Cookie`);
      
      // 恢复每个Cookie
      for (const cookie of cookies) {
        try {
          await restoreSingleCookie(cookie);
          mainCookiesRestored++;
        } catch (err) {
          console.error(`从主文件恢复Cookie ${cookie.name} 失败:`, err);
        }
      }
      
      console.log(`成功从主文件恢复 ${mainCookiesRestored} 个Cookie`);
    } else {
      console.log('没有找到主Cookie文件');
    }
    
    // 尝试从域名特定的Cookie文件恢复
    try {
      const userDataPath = app.getPath('userData');
      const files = fs.readdirSync(userDataPath);
      const cookieFiles = files.filter(file => file.startsWith('cookies-') && file.endsWith('.json'));
      
      let domainCookiesRestored = 0;
      for (const file of cookieFiles) {
        try {
          const filePath = path.join(userDataPath, file);
          const cookiesData = fs.readFileSync(filePath, 'utf-8');
          const cookies = JSON.parse(cookiesData);
          
          console.log(`从域名文件 ${file} 恢复 ${cookies.length} 个Cookie`);
          
          // 恢复每个Cookie
          for (const cookie of cookies) {
            try {
              await restoreSingleCookie(cookie);
              domainCookiesRestored++;
            } catch (err) {
              console.error(`从域名文件恢复Cookie ${cookie.name} 失败:`, err);
            }
          }
        } catch (err) {
          console.error(`处理域名Cookie文件 ${file} 失败:`, err);
        }
      }
      
      console.log(`成功从域名文件恢复 ${domainCookiesRestored} 个Cookie`);
    } catch (err) {
      console.error('从域名Cookie文件恢复失败:', err);
    }
    
    // 尝试从document.cookie文件恢复
    try {
      const docCookiesPath = path.join(app.getPath('userData'), 'document-cookies.txt');
      if (fs.existsSync(docCookiesPath)) {
        const docCookies = fs.readFileSync(docCookiesPath, 'utf-8');
        console.log(`从document.cookie文件读取: ${docCookies.substring(0, 100)}...`);
        
        // 这些Cookie将在页面加载时通过preload脚本设置
        console.log('这些Cookie将在页面加载时通过preload脚本设置');
      }
    } catch (err) {
      console.error('读取document.cookie文件失败:', err);
    }
    
    // 尝试恢复localStorage数据
    try {
      if (fs.existsSync(LOCAL_STORAGE_PATH)) {
        const localStorageData = JSON.parse(fs.readFileSync(LOCAL_STORAGE_PATH, 'utf-8'));
        console.log(`尝试恢复 ${Object.keys(localStorageData).length} 个localStorage项`);
        
        // 这里我们不能直接设置localStorage，但可以确保相关的Cookie已经恢复
      }
    } catch (err) {
      console.error('恢复localStorage数据失败:', err);
    }
    
    console.log('Cookie恢复完成');
  } catch (error) {
    console.error('恢复Cookie失败:', error);
  }
}

// 恢复单个Cookie的辅助函数
async function restoreSingleCookie(cookie) {
  // 确保Cookie不会立即过期
  cookie.expirationDate = Math.floor(Date.now() / 1000) + 86400 * 365; // 一年
  
  // 确保httpOnly和secure属性正确设置
  if (cookie.secure === undefined) cookie.secure = true;
  if (cookie.httpOnly === undefined) cookie.httpOnly = true;
  
  // 处理域名问题
  if (cookie.domain.startsWith('.')) {
    cookie.domain = cookie.domain.substring(1);
  }
  
  // 确保路径属性存在
  if (!cookie.path) cookie.path = '/';
  
  // 构建正确的URL
  let url;
  if (cookie.domain.includes('weixin.qq.com') ||
      cookie.domain.includes('store.weixin.qq.com') ||
      cookie.domain.includes('filehelper.weixin.qq.com') ||
      cookie.domain.includes('channels.weixin.qq.com') ||
      cookie.domain.includes('wx.qq.com')) {
    url = `https://${cookie.domain}${cookie.path}`;
  } else {
    url = cookie.secure 
      ? `https://${cookie.domain}${cookie.path}` 
      : `http://${cookie.domain}${cookie.path}`;
  }
  
  // 设置Cookie
  await session.defaultSession.cookies.set({
    url: url,
    name: cookie.name,
    value: cookie.value,
    domain: cookie.domain,
    path: cookie.path,
    secure: cookie.secure,
    httpOnly: cookie.httpOnly,
    expirationDate: cookie.expirationDate,
    sameSite: cookie.sameSite || 'no_restriction'
  });
  
  console.log(`恢复Cookie: ${cookie.name}=${cookie.value.substring(0, 10)}... (domain: ${cookie.domain}, url: ${url})`);
}

// 创建登录窗口
function createLoginWindow() {
  loginWindow = new BrowserWindow({
    width: 420, // 窗口宽度刚好等于内容宽度
    height: 580, // 调整高度以适应内容
    resizable: false,
    fullscreenable: false,
    maximizable: false,
    title: '小梅花AI智能客服 - 登录',
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: process.env.NODE_ENV === 'development',
      partition: 'persist:main' // 使用持久化分区
    },
    frame: false, // 无边框窗口
    transparent: true, // 窗口透明
    backgroundColor: '#00000000', // 完全透明的背景色
    hasShadow: false, // 禁用窗口阴影
    icon: path.join(__dirname, '../build/icon.png'),
    show: false // 先不显示窗口，等加载完成后再显示
  });

  // 加载登录页面
  loginWindow.loadFile(path.join(__dirname, 'renderer/login.html'));
  
  // 页面加载完成后再显示窗口，避免白屏
  loginWindow.once('ready-to-show', () => {
    loginWindow.show();

    // 【修复粘贴功能】确保登录窗口支持粘贴操作
    loginWindow.webContents.on('before-input-event', (event, input) => {
      // 明确允许粘贴相关的快捷键
      const isPasteShortcut =
        (input.control && input.key.toLowerCase() === 'v') ||
        (input.meta && input.key.toLowerCase() === 'v') ||
        (input.control && input.key.toLowerCase() === 'a') ||
        (input.meta && input.key.toLowerCase() === 'a');

      if (isPasteShortcut) {
        // 明确不阻止粘贴和全选操作
        console.log('允许粘贴/全选快捷键:', input.key);
        return;
      }
    });

    // 【新增】为登录窗口设置同样的优化菜单
    if (process.platform === 'darwin') {
      const loginMenuTemplate = [
        {
          label: app.getName(), // 使用应用名称作为菜单标签
          submenu: [
            {
              label: '关于小梅花AI智能客服',
              click: () => {
                showAboutWindow();
              }
            },
            { type: 'separator' },
            {
              label: '退出',
              accelerator: 'Cmd+Q',
              click: () => app.quit()
            }
          ]
        }
      ];

      const loginMenu = Menu.buildFromTemplate(loginMenuTemplate);
      Menu.setApplicationMenu(loginMenu);
    } else {
      // 非macOS平台隐藏菜单栏
      Menu.setApplicationMenu(null);
    }

    // 检查是否是手动退出登录
    const isManualLogout = store.get('manual_logout');
    const logoutSavedLicense = store.get('logout_saved_license');

    // 【新增】如果是手动退出登录且有保存的卡密，自动填充但不自动验证
    if (isManualLogout && logoutSavedLicense) {
      setTimeout(() => {
        console.log('退出登录后自动填充卡密');
        loginWindow.webContents.send('auto-fill-license', logoutSavedLicense);
      }, 500); // 延迟500ms再自动填充

      // 清除保存的退出登录卡密
      store.delete('logout_saved_license');
      // 清除手动退出登录标记，以便下次启动时可以自动登录
      store.delete('manual_logout');
    } else if (!isManualLogout) {
      // 非手动退出登录的情况下，自动验证已保存的卡密
      const savedLicense = store.get('license');
      if (savedLicense) {
        setTimeout(() => {
          loginWindow.webContents.send('auto-verify', savedLicense);
        }, 500); // 延迟500ms再自动验证
      }
    } else {
      // 清除手动退出登录标记，以便下次启动时可以自动登录
      store.delete('manual_logout');
    }
  });

  // 窗口关闭处理
  loginWindow.on('closed', () => {
    loginWindow = null;
  });
  
  // 修改登录窗口的关闭事件处理
  loginWindow.on('close', (event) => {
    // 只有在用户手动点击关闭按钮时才退出应用程序
    // 如果是因为验证成功而关闭，则不执行退出操作
    if (!isQuitting && !mainWindow) {
      app.quit();
    }
  });
}

// 创建主窗口
function createMainWindow(shopInfo) {
  // 如果窗口已存在，则激活它
  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.show();
    mainWindow.webContents.send('shop-info', shopInfo);
    return;
  }

  // 基础窗口配置
  const windowOptions = {
    width: 1280,
    height: 800,
    title: '小梅花AI智能客服',
    backgroundColor: '#ffffff',
    icon: path.join(__dirname, '../build/icon.png'),
    show: false, // 先不显示窗口，等加载完成后再显示
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: true, // 始终启用开发者工具，方便调试
      webSecurity: false, // 允许跨域请求
      allowRunningInsecureContent: true, // 允许运行不安全的内容
      experimentalFeatures: true, // 启用实验性功能
      webviewTag: true, // 允许使用webview标签
      javascript: true, // 允许JavaScript
      plugins: true, // 允许插件
      images: true, // 允许图片
      textAreasAreResizable: true, // 允许文本区域调整大小
      navigateOnDragDrop: true, // 允许拖放导航
      autoplayPolicy: 'no-user-gesture-required', // 允许自动播放
      backgroundThrottling: false, // 【关键优化】禁用后台节流，确保最小化时功能正常
      // 【新增】确保最小化时页面保持活跃状态的配置
      offscreen: false, // 禁用离屏渲染，确保页面始终活跃
      enableWebSQL: false, // 禁用WebSQL以提高性能
      spellcheck: false, // 禁用拼写检查以提高性能
      // 【关键】确保页面在后台时不被暂停
      pageVisibility: false, // 禁用页面可见性API的影响
    }
  };

  // 根据平台添加特定配置
  if (process.platform === 'darwin') {
    // macOS特有配置 - 使用系统原生红绿灯按钮，避免重复
    windowOptions.titleBarStyle = 'hiddenInset'; // 使用hiddenInset样式，保留原生按钮
    windowOptions.trafficLightPosition = { x: 15, y: 15 }; // 调整红绿灯按钮位置
    windowOptions.frame = true; // 保持窗口边框以支持原生按钮
  } else {
    // Windows特有配置
    windowOptions.frame = false; // 无边框窗口
    // 【Windows平台优化】添加持久化会话配置
    windowOptions.webPreferences.partition = 'persist:main';
    windowOptions.webPreferences.enableRemoteModule = false;
    windowOptions.webPreferences.nodeIntegrationInWorker = false;
  }

  // 创建窗口
  mainWindow = new BrowserWindow(windowOptions);

  // 设置User-Agent以便网站识别APP访问
  const session = mainWindow.webContents.session;
  session.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Electron/28.0.0 Safari/537.36 xiaomeihua-app/1.0.0');

  // 预加载店铺信息，使渲染进程可以立即访问
  global.shopInfoCache = shopInfo;

  // 加载主界面
  mainWindow.loadFile(path.join(__dirname, 'renderer/main.html'));

  // 页面加载完成后再显示窗口，避免白屏
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // 传递店铺信息 - 确保页面已经准备好接收消息
    setTimeout(() => {
      if (mainWindow && !mainWindow.isDestroyed()) {
        console.log('向主窗口发送店铺信息:', JSON.stringify(shopInfo));
        mainWindow.webContents.send('shop-info', shopInfo);
      }
    }, 500); // 增加延迟时间，确保页面完全加载
  });

  // 设置优化的macOS菜单 - 只保留应用菜单，移除View、File、Edit、Window、Help等英文菜单
  if (process.platform === 'darwin') {
    const menuTemplate = [
      {
        label: app.getName(), // 使用应用名称作为菜单标签
        submenu: [
          {
            label: '关于小梅花AI智能客服',
            click: () => {
              showAboutWindow();
            }
          },
          { type: 'separator' },
          {
            label: '退出',
            accelerator: 'Cmd+Q',
            click: () => app.quit()
          }
        ]
      }
    ];

    const menu = Menu.buildFromTemplate(menuTemplate);
    Menu.setApplicationMenu(menu);
  } else {
    // 非macOS平台隐藏菜单栏
    Menu.setApplicationMenu(null);
  }
  
  // 禁用开发者工具快捷键
  mainWindow.webContents.on('before-input-event', (event, input) => {
    // 禁用 F12, Ctrl+Shift+I, Cmd+Alt+I 等开发者工具快捷键
    const isDeveloperShortcut = 
      (input.key === 'F12') || 
      (input.control && input.shift && input.key.toLowerCase() === 'i') || 
      (input.meta && input.alt && input.key.toLowerCase() === 'i');
    
    if (isDeveloperShortcut) {
      event.preventDefault();
    }
  });

  // 【新增】监听窗口最小化事件，确保功能不受影响
  mainWindow.on('minimize', () => {
    console.log('🔽 主窗口已最小化，确保后台功能正常运行...');

    // 确保所有webview在最小化时保持活跃状态
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.executeJavaScript(`
        // 确保所有webview在最小化时保持活跃
        const webviews = document.querySelectorAll('webview');
        webviews.forEach((webview, index) => {
          if (webview && webview.getWebContents) {
            try {
              // 设置webview保持活跃状态
              webview.getWebContents().setBackgroundThrottling(false);
              console.log('已设置webview', index, '在最小化时保持活跃状态');
            } catch (err) {
              console.warn('设置webview活跃状态失败:', err);
            }
          }
        });

        // 确保定时器和事件监听器继续工作
        if (window.xiaomeihuaAPI && window.xiaomeihuaAPI.ensureBackgroundActivity) {
          window.xiaomeihuaAPI.ensureBackgroundActivity();
        }

        console.log('✅ 最小化时后台活动保持设置完成');
      `).catch(err => {
        console.error('设置最小化时后台活动失败:', err);
      });
    }
  });

  // 【新增】监听窗口恢复事件
  mainWindow.on('restore', () => {
    console.log('🔼 主窗口已恢复显示');

    // 恢复时重新激活所有功能
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.executeJavaScript(`
        console.log('🔄 窗口恢复，重新激活所有功能...');

        // 重新检查所有webview状态
        const webviews = document.querySelectorAll('webview');
        webviews.forEach((webview, index) => {
          if (webview && webview.src) {
            console.log('检查webview', index, '状态:', webview.src);
          }
        });

        // 触发功能重新激活
        if (window.xiaomeihuaAPI && window.xiaomeihuaAPI.reactivateFeatures) {
          window.xiaomeihuaAPI.reactivateFeatures();
        }

        console.log('✅ 窗口恢复时功能重新激活完成');
      `).catch(err => {
        console.error('窗口恢复时重新激活功能失败:', err);
      });
    }
  });

  // 监听窗口关闭事件
  mainWindow.on('close', (event) => {
    // 直接退出应用程序，不再隐藏窗口
    app.quit();
  });

  // 如果登录窗口存在，关闭它
  if (loginWindow && !loginWindow.isDestroyed()) {
    loginWindow.close();
    loginWindow = null;
  }
}

// 创建店铺浏览器窗口
function createShopBrowserWindow(shopId, shopName, url) {
  console.log(`创建店铺浏览器窗口: ${shopName} (${shopId}), URL: ${url}`);
  
  // 【修复】为每个店铺创建独立的会话，确保数据不互通
  const shopPartition = `persist:shop_${shopId}`;
  const shopSession = session.fromPartition(shopPartition);
  
  // 确保Cookie管理器知道这个新的会话分区
  const cookieManagerSession = cookieManager.getOrCreateSession(shopPartition);
  
  // 创建独立的BrowserWindow
  const windowOptions = {
    width: 1280,
    height: 800,
    title: `${shopName}`,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload-browser.js'),
      webSecurity: false,
      allowRunningInsecureContent: true,
      webviewTag: true,
      session: shopSession, // 使用店铺特定的会话
      clearCache: false,
      clearStorageData: false,
      // 【关键优化】确保店铺窗口最小化时功能正常
      backgroundThrottling: false, // 禁用后台节流
      offscreen: false, // 禁用离屏渲染
      pageVisibility: false, // 禁用页面可见性API的影响
    }
  };

  // 根据平台设置窗口样式
  if (process.platform === 'darwin') {
    // macOS: 使用系统原生红绿灯按钮
    windowOptions.titleBarStyle = 'hiddenInset';
    windowOptions.trafficLightPosition = { x: 15, y: 15 };
    windowOptions.frame = true;
  } else {
    // Windows: 无边框窗口
    windowOptions.frame = false;
  }

  const win = new BrowserWindow(windowOptions);

  // 设置User-Agent以便网站识别APP访问
  shopSession.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Electron/28.0.0 Safari/537.36 xiaomeihua-app/1.0.0');

  // 【新增】监听店铺窗口最小化事件
  win.on('minimize', () => {
    console.log(`🔽 店铺窗口 ${shopName} 已最小化，确保后台功能正常运行...`);

    // 确保店铺窗口在最小化时保持活跃状态
    if (win && !win.isDestroyed()) {
      win.webContents.setBackgroundThrottling(false);

      win.webContents.executeJavaScript(`
        console.log('🔄 店铺窗口最小化，设置后台保持活跃...');

        // 确保页面定时器和事件监听器继续工作
        if (window.document && window.document.hidden !== undefined) {
          // 重写document.hidden属性，使页面认为自己始终可见
          Object.defineProperty(document, 'hidden', {
            get: function() { return false; },
            configurable: true
          });

          // 重写document.visibilityState属性
          Object.defineProperty(document, 'visibilityState', {
            get: function() { return 'visible'; },
            configurable: true
          });
        }

        console.log('✅ 店铺窗口最小化时后台活动保持设置完成');
      `).catch(err => {
        console.error(`设置店铺窗口 ${shopName} 最小化时后台活动失败:`, err);
      });
    }
  });

  // 【新增】监听店铺窗口恢复事件
  win.on('restore', () => {
    console.log(`🔼 店铺窗口 ${shopName} 已恢复显示`);

    // 恢复时重新激活所有功能
    if (win && !win.isDestroyed()) {
      win.webContents.executeJavaScript(`
        console.log('🔄 店铺窗口恢复，重新激活所有功能...');

        // 触发页面重新检查状态
        if (window.location && window.location.reload) {
          // 不重新加载页面，只是触发状态检查
          console.log('店铺窗口恢复，当前URL:', window.location.href);
        }

        console.log('✅ 店铺窗口恢复时功能重新激活完成');
      `).catch(err => {
        console.error(`店铺窗口 ${shopName} 恢复时重新激活功能失败:`, err);
      });
    }
  });

  // 监听窗口关闭事件
  win.on('close', () => {
    delete shopWindows[shopId];
  });

  // 准备恢复localStorage数据
  const localStorageData = store.get(`shop-localstorage-${shopId}`);
  if (localStorageData) {
    win.webContents.on('did-finish-load', () => {
      win.webContents.executeJavaScript(`
        Object.entries(${JSON.stringify(localStorageData)}).forEach(([key, value]) => {
          localStorage.setItem(key, value);
        });
      `);
    });
  }

  // 监听页面标题变化，处理preload-browser.js发送的IPC消息
  win.webContents.on('page-title-updated', (event, title) => {
    if (title.startsWith('XMH_IPC_MSG::')) {
      try {
        const messageData = title.replace('XMH_IPC_MSG::', '');
        const ipcCommand = JSON.parse(messageData);
        console.log(`[主进程] 收到来自preload-browser的IPC消息: ${ipcCommand.channel}`, ipcCommand.data);

        // 处理不同的IPC消息
        handlePreloadIpcMessage(ipcCommand.channel, ipcCommand.data, win);
      } catch (error) {
        console.error('[主进程] 解析preload-browser IPC消息失败:', error);
      }
    }
  });

  // 在窗口加载完成后注入脚本（Cookie已在应用启动时恢复）
  win.webContents.once('did-finish-load', async () => {
    console.log(`✅ 店铺窗口 ${shopName} 加载完成`);

    try {
      // 注入脚本
      injectScript(win.webContents);

      // 延迟检查是否需要刷新页面以应用Cookie
      setTimeout(async () => {
        try {
          const needsRefresh = await checkIfNeedsRefresh(win);
          if (needsRefresh) {
            console.log(`[${shopName}] 检测到需要刷新页面以应用Cookie`);
            win.webContents.reload();
          }
        } catch (err) {
          console.error(`[${shopName}] 延迟检查失败:`, err);
        }
      }, 2000);

    } catch (error) {
      console.error(`❌ 店铺窗口 ${shopName} 处理失败:`, error);
    }
  });

  // 加载URL
  win.loadURL(url);

  shopWindows[shopId] = win;
}

// 设置域名过滤
function setupDomainFilter(win) {
  win.webContents.session.webRequest.onBeforeRequest({ urls: ['*://*/*'] }, (details, callback) => {
    // 不再限制任何域名访问，允许访问所有网站
    callback({ cancel: false });
    
    // 记录访问日志，但不阻止
    const url = new URL(details.url);
    const hostname = url.hostname;
    
    // 仅对非常规域名记录日志，减少日志量
    let isCommonDomain = false;
    for (const domain of ALLOWED_DOMAINS) {
      if (hostname === domain || hostname.endsWith('.' + domain)) {
        isCommonDomain = true;
        break;
      }
    }
    
    if (!isCommonDomain) {
      console.log(`访问网站: ${details.url}`);
    }
  });
}

// 解压.crx文件到扩展目录
async function extractCrxFile(crxPath, extractDir) {
  try {
    console.log(`开始解压扩展文件: ${crxPath} 到 ${extractDir}`);
    
    // 确保目标目录存在
    if (!fs.existsSync(extractDir)) {
      fs.mkdirSync(extractDir, { recursive: true });
    }
    
    // 读取.crx文件
    const crxData = fs.readFileSync(crxPath);
    
    // CRX文件格式: 
    // 前12字节是头部信息，包括魔数"Cr24"
    // 接下来4字节是版本号
    // 接下来4字节是公钥长度
    // 接下来4字节是签名长度
    // 然后是公钥和签名
    // 剩余部分是ZIP数据
    
    // 跳过头部和公钥签名部分，直接提取ZIP数据
    let offset = 12; // 跳过魔数和版本号
    
    // 读取公钥长度和签名长度
    const publicKeyLength = crxData.readUInt32LE(offset);
    offset += 4;
    
    const signatureLength = crxData.readUInt32LE(offset);
    offset += 4;
    
    // 跳过公钥和签名
    offset += publicKeyLength + signatureLength;
    
    // 提取ZIP数据
    const zipData = crxData.slice(offset);
    
    // 创建临时ZIP文件
    const tempZipPath = path.join(app.getPath('temp'), 'tampermonkey.zip');
    fs.writeFileSync(tempZipPath, zipData);
    
    // 使用内置模块解压ZIP文件
    const { exec } = require('child_process');
    
    // 清空目标目录
    if (fs.existsSync(extractDir)) {
      const files = fs.readdirSync(extractDir);
      for (const file of files) {
        const filePath = path.join(extractDir, file);
        if (fs.lstatSync(filePath).isDirectory()) {
          fs.rmdirSync(filePath, { recursive: true });
        } else {
          fs.unlinkSync(filePath);
        }
      }
    }
    
    // 根据操作系统选择解压命令
    let extractCommand;
    if (process.platform === 'win32') {
      // Windows
      extractCommand = `powershell -command "Expand-Archive -Path '${tempZipPath}' -DestinationPath '${extractDir}' -Force"`;
    } else {
      // macOS/Linux
      extractCommand = `unzip -o "${tempZipPath}" -d "${extractDir}"`;
    }
    
    // 执行解压命令
    return new Promise((resolve, reject) => {
      exec(extractCommand, (error, stdout, stderr) => {
        // 删除临时ZIP文件
        try {
          fs.unlinkSync(tempZipPath);
        } catch (e) {
          console.error('删除临时ZIP文件失败:', e);
        }
        
        if (error) {
          console.error('解压失败:', error);
          console.error('stderr:', stderr);
          reject(error);
        } else {
          console.log('解压成功:', stdout);
          resolve(extractDir);
        }
      });
    });
  } catch (error) {
    console.error('解压扩展文件失败:', error);
    throw error;
  }
}

// 设置扩展目录 - 优化扩展加载流程
async function setupExtensions() {
  try {
    console.log('设置扩展目录...');
    
    // 确定扩展目录路径
    const extensionsDir = path.join(app.getPath('userData'), 'extensions');
    
    // 确保目录存在
    if (!fs.existsSync(extensionsDir)) {
      console.log('创建扩展目录:', extensionsDir);
      fs.mkdirSync(extensionsDir, { recursive: true });
    }
    
    // 查找Tampermonkey扩展源文件
    let tampermonkeySource = null;
    const resourcePath = path.join(process.resourcesPath, 'resources', 'tampermonkey_stable.crx');
    const devPath = path.join(__dirname, '../resources/tampermonkey_stable.crx');
    const parentPath = path.join(__dirname, '../tampermonkey_stable.crx');
    
    // 按优先级查找扩展源文件
    if (fs.existsSync(resourcePath)) {
      tampermonkeySource = resourcePath;
    } else if (fs.existsSync(devPath)) {
      tampermonkeySource = devPath;
    } else if (fs.existsSync(parentPath)) {
      tampermonkeySource = parentPath;
    }
    
    if (tampermonkeySource) {
      console.log(`找到Tampermonkey扩展源文件: ${tampermonkeySource}`);
      
      // 为Tampermonkey创建专用目录
      const tampermonkeyDir = path.join(extensionsDir, 'tampermonkey');
      if (!fs.existsSync(tampermonkeyDir)) {
        fs.mkdirSync(tampermonkeyDir, { recursive: true });
      }
      
      // 检查是否已经存在解压后的扩展
      const manifestPath = path.join(tampermonkeyDir, 'manifest.json');
      let needExtract = true;
      
      if (fs.existsSync(manifestPath)) {
        try {
          const manifestData = fs.readFileSync(manifestPath, 'utf8');
          const manifest = JSON.parse(manifestData);
          
          // 检查扩展文件是否完整
          const requiredFiles = ['manifest.json', 'background.js', 'content.js'];
          const allFilesExist = requiredFiles.every(file => fs.existsSync(path.join(tampermonkeyDir, file)));
          
          if (allFilesExist) {
            needExtract = false;
          }
        } catch (err) {
          console.error('读取manifest.json失败，需要重新解压:', err);
        }
      }
      
      // 如果需要，解压扩展文件
      if (needExtract) {
        console.log('开始解压Tampermonkey扩展...');
        try {
          await extractCrxFile(tampermonkeySource, tampermonkeyDir);
          console.log('Tampermonkey扩展解压成功');
        } catch (extractError) {
          console.error('解压Tampermonkey扩展失败:', extractError);
        }
      }
      
      // 加载扩展到默认会话 - 使用延迟加载
      setTimeout(async () => {
        try {
          // 检查扩展目录是否有效
          if (fs.existsSync(path.join(tampermonkeyDir, 'manifest.json'))) {
            const extInfo = await session.defaultSession.loadExtension(tampermonkeyDir, {
              allowFileAccess: true
            });
            
            console.log('✅ Tampermonkey扩展加载成功:', extInfo.id);
            
            // 存储扩展ID和路径
            store.set('tampermonkey_extension_id', extInfo.id);
            store.set('tampermonkey_extension_path', tampermonkeyDir);
          }
        } catch (loadError) {
          console.error('加载Tampermonkey扩展失败:', loadError);
        }
      }, 1000); // 延迟1秒加载扩展
    } else {
      console.error('❌ 未找到Tampermonkey扩展源文件');
    }
  } catch (error) {
    console.error('设置扩展目录失败:', error);
  }
}

// 加载Tampermonkey扩展
function loadTampermonkeyExtension(sessionInstance) {
  try {
    console.log('开始加载Tampermonkey扩展...');
    
    // 获取扩展源文件路径
    let tampermonkeySource = null;
    const resourcePath = path.join(process.resourcesPath, 'resources', 'tampermonkey_stable.crx');
    const devPath = path.join(__dirname, '../resources/tampermonkey_stable.crx');
    const parentPath = path.join(__dirname, '../tampermonkey_stable.crx');
    
    if (fs.existsSync(resourcePath)) {
      tampermonkeySource = resourcePath;
      console.log('从资源目录加载Tampermonkey扩展:', resourcePath);
    } else if (fs.existsSync(devPath)) {
      tampermonkeySource = devPath;
      console.log('从开发目录加载Tampermonkey扩展:', devPath);
    } else if (fs.existsSync(parentPath)) {
      tampermonkeySource = parentPath;
      console.log('从上级目录加载Tampermonkey扩展:', parentPath);
    } else {
      throw new Error('无法找到Tampermonkey扩展源文件');
    }
    
    console.log(`找到Tampermonkey扩展: ${tampermonkeySource}`);
    
    // 验证扩展文件是否存在且有效
    try {
      const stats = fs.statSync(tampermonkeySource);
      if (!stats.isFile() || stats.size < 1000) {
        throw new Error(`扩展文件无效或损坏: ${tampermonkeySource}, 大小: ${stats.size} 字节`);
      }
      console.log(`扩展文件有效，大小: ${stats.size} 字节`);
    } catch (statError) {
      console.error('扩展文件检查失败:', statError);
      throw new Error('扩展文件无效或无法访问');
    }
    
    // 创建扩展目录
    const extensionsDir = path.join(app.getPath('userData'), 'extensions', 'tampermonkey');
    if (!fs.existsSync(extensionsDir)) {
      fs.mkdirSync(extensionsDir, { recursive: true });
      console.log(`创建扩展目录: ${extensionsDir}`);
    }
    
    // 解压并加载扩展
    console.log('开始解压并加载扩展...');
    extractAndLoadExtension(sessionInstance, tampermonkeySource)
      .then((extInfo) => {
        console.log('✅ Tampermonkey扩展加载成功:', extInfo.id);
        
        // 存储扩展ID以便后续使用
        if (extInfo && extInfo.id) {
          store.set('tampermonkey_extension_id', extInfo.id);
        }
        
        // 配置Tampermonkey扩展
        configureTampermonkey(sessionInstance, extInfo.id);
      })
      .catch(err => {
        console.error('❌ Tampermonkey扩展加载失败:', err);
        
        // 尝试使用其他方法加载
        console.log('尝试使用备用方法加载扩展...');
        
        // 使用默认会话的扩展ID
        const extensionId = store.get('tampermonkey_extension_id');
        if (extensionId) {
          console.log(`使用已加载的扩展ID: ${extensionId}`);
          // 配置Tampermonkey扩展
          configureTampermonkey(sessionInstance, extensionId);
        } else {
          console.error('无法加载Tampermonkey扩展，尝试其他方法');
          
          // 尝试直接从解压目录加载
          const tempExtDir = path.join(app.getPath('temp'), 'tampermonkey_extension');
          if (fs.existsSync(tempExtDir)) {
            console.log(`尝试从临时目录加载扩展: ${tempExtDir}`);
            sessionInstance.loadExtension(tempExtDir, { allowFileAccess: true })
              .then(extInfo => {
                console.log('✅ 从临时目录加载Tampermonkey扩展成功:', extInfo.id);
                store.set('tampermonkey_extension_id', extInfo.id);
                configureTampermonkey(sessionInstance, extInfo.id);
              })
              .catch(tempError => {
                console.error('从临时目录加载扩展失败:', tempError);
                
                // 最后的备用方案：直接注入脚本
                console.log('尝试直接注入脚本而不使用扩展...');
                // 设置一个标记，表示需要直接注入脚本
                store.set('use_direct_script_injection', true);
              });
          } else {
            // 创建临时目录并解压
            console.log('创建临时目录并解压扩展...');
            fs.mkdirSync(tempExtDir, { recursive: true });
            extractCrxFile(tampermonkeySource, tempExtDir)
              .then(() => {
                sessionInstance.loadExtension(tempExtDir, { allowFileAccess: true })
                  .then(extInfo => {
                    console.log('✅ 从新创建的临时目录加载扩展成功:', extInfo.id);
                    store.set('tampermonkey_extension_id', extInfo.id);
                    configureTampermonkey(sessionInstance, extInfo.id);
                  })
                  .catch(newTempError => {
                    console.error('从新创建的临时目录加载扩展失败:', newTempError);
                    // 设置直接注入标记
                    store.set('use_direct_script_injection', true);
                  });
              })
              .catch(extractError => {
                console.error('解压到临时目录失败:', extractError);
                // 设置直接注入标记
                store.set('use_direct_script_injection', true);
              });
          }
        }
      });
  } catch (error) {
    console.error('加载Tampermonkey扩展过程中发生错误:', error);
    
    // 尝试解压CRX文件并加载
    try {
      const resourcePath = path.join(process.resourcesPath, 'resources', 'tampermonkey_stable.crx');
      const devPath = path.join(__dirname, '../resources/tampermonkey_stable.crx');
      const parentPath = path.join(__dirname, '../tampermonkey_stable.crx');
      
      let tampermonkeySource = null;
      if (fs.existsSync(resourcePath)) {
        tampermonkeySource = resourcePath;
      } else if (fs.existsSync(devPath)) {
        tampermonkeySource = devPath;
      } else if (fs.existsSync(parentPath)) {
        tampermonkeySource = parentPath;
      }
      
      if (tampermonkeySource) {
        extractAndLoadExtension(sessionInstance, tampermonkeySource)
          .catch(finalError => {
            console.error('最终尝试加载扩展失败:', finalError);
            // 设置直接注入标记
            store.set('use_direct_script_injection', true);
          });
      } else {
        console.error('找不到任何可用的扩展源文件');
        // 设置直接注入标记
        store.set('use_direct_script_injection', true);
      }
    } catch (extractError) {
      console.error('解压并加载扩展失败:', extractError);
      // 设置直接注入标记
      store.set('use_direct_script_injection', true);
    }
  }
}

// 配置Tampermonkey扩展
async function configureTampermonkey(sessionInstance, extensionId) {
  try {
    console.log(`配置Tampermonkey扩展 ID: ${extensionId}`);
    
    // 确保扩展已加载
    if (!extensionId) {
      console.error('无法配置Tampermonkey扩展: 缺少扩展ID');
      return;
    }
    
    // 获取卡密验证脚本
    const { getKamiyanScript } = require('./kamiyan-injector');
    const script = getKamiyanScript();
    
    if (!script) {
      console.error('无法获取卡密验证脚本');
      return;
    }
    
    // 获取许可证密钥和店铺信息
    const licenseKey = store.get('license');
    const shopInfo = store.get('shop_info');
    
    if (!licenseKey) {
      console.error('无法配置Tampermonkey扩展: 缺少许可证密钥');
      return;
    }
    
    // 创建用户脚本
    const userScript = `
      // ==UserScript==
      // @name         小梅花AI客服助手
      // @namespace    http://xiaomeihuakefu.cn/
      // @version      5.0.5
      // @description  小梅花AI智能客服助手
      // <AUTHOR>
      // @match        https://store.weixin.qq.com/shop/kf*
      // @grant        GM_xmlhttpRequest
      // @grant        GM_setValue
      // @grant        GM_getValue
      // @grant        GM_deleteValue
      // @grant        GM_addStyle
      // @grant        GM_openInTab
      // @grant        window.open
      // @grant        unsafeWindow
      // @connect      xiaomeihuakefu.cn
      // @connect      *
      // ==/UserScript==

      (function() {
        'use strict';
        
        // 设置全局卡密变量
        window.xiaomeihuaLicenseKey = "${licenseKey}";
        
        // 设置店铺信息
        window.xiaomeihuaShopInfo = ${JSON.stringify(shopInfo || {})};
        
        // 执行卡密验证脚本
        ${script}
      })();
    `;
    
    // 获取扩展安装路径
    const extensionPath = store.get('tampermonkey_extension_path');
    if (!extensionPath || !fs.existsSync(extensionPath)) {
      console.error('无法找到Tampermonkey扩展安装路径');
      return;
    }
    
    console.log(`Tampermonkey扩展安装路径: ${extensionPath}`);
    
    // 创建用户脚本目录
    const scriptsDir = path.join(extensionPath, 'scripts');
    if (!fs.existsSync(scriptsDir)) {
      fs.mkdirSync(scriptsDir, { recursive: true });
    }
    
    // 保存用户脚本到扩展的scripts目录
    const scriptFileName = 'xiaomeihua_ai_assistant.user.js';
    const scriptPath = path.join(scriptsDir, scriptFileName);
    fs.writeFileSync(scriptPath, userScript);
    
    console.log(`用户脚本已保存到扩展目录: ${scriptPath}`);
    
    // 创建脚本配置文件
    const scriptConfig = {
      enabled: true,
      position: 1,
      name: "小梅花AI客服助手",
      namespace: "http://xiaomeihuakefu.cn/",
      version: "5.0.5",
      description: "小梅花AI智能客服助手",
      excludes: [],
      includes: [],
      matches: ["https://store.weixin.qq.com/shop/kf*"],
      "run-at": "document-idle",
      uuid: uuidv4()
    };
    
    // 保存脚本配置
    const configDir = path.join(extensionPath, 'config');
    if (!fs.existsSync(configDir)) {
      fs.mkdirSync(configDir, { recursive: true });
    }
    
    const configPath = path.join(configDir, 'script_config.json');
    
    // 如果配置文件已存在，读取并更新
    let existingConfig = [];
    if (fs.existsSync(configPath)) {
      try {
        const configData = fs.readFileSync(configPath, 'utf8');
        existingConfig = JSON.parse(configData);
        
        // 检查是否已存在相同名称的脚本
        const existingScriptIndex = existingConfig.findIndex(script => script.name === scriptConfig.name);
        if (existingScriptIndex >= 0) {
          // 更新现有脚本
          console.log('更新现有脚本配置');
          existingConfig[existingScriptIndex] = scriptConfig;
        } else {
          // 添加新脚本
          console.log('添加新脚本配置');
          existingConfig.push(scriptConfig);
        }
      } catch (err) {
        console.error('读取脚本配置失败，创建新配置:', err);
        existingConfig = [scriptConfig];
      }
    } else {
      // 创建新配置文件
      existingConfig = [scriptConfig];
    }
    
    // 写入配置文件
    fs.writeFileSync(configPath, JSON.stringify(existingConfig, null, 2));
    console.log('脚本配置已保存');
    
    // 创建一个标记文件，表示脚本已安装
    const flagPath = path.join(extensionPath, 'xiaomeihua_script_installed');
    fs.writeFileSync(flagPath, Date.now().toString());
    
    console.log('Tampermonkey扩展配置完成');
    
    // 同时保存一个临时文件，以便在需要时直接注入
    const tempScriptPath = path.join(app.getPath('temp'), 'xiaomeihua_script.js');
    fs.writeFileSync(tempScriptPath, userScript);
    console.log('用户脚本也已保存到临时文件:', tempScriptPath);
  } catch (error) {
    console.error('配置Tampermonkey扩展失败:', error);
  }
}

// 解压并加载扩展
async function extractAndLoadExtension(sessionInstance, crxPath) {
  try {
    console.log('解压并加载扩展:', crxPath);
    
    // 创建扩展目录
    const extensionsDir = path.join(app.getPath('userData'), 'extensions');
    if (!fs.existsSync(extensionsDir)) {
      fs.mkdirSync(extensionsDir, { recursive: true });
    }
    
    // 为Tampermonkey创建唯一目录
    const tampermonkeyDir = path.join(extensionsDir, 'tampermonkey_' + Date.now().toString());
    if (!fs.existsSync(tampermonkeyDir)) {
      fs.mkdirSync(tampermonkeyDir, { recursive: true });
    }
    
    // 解压CRX文件
    await extractCrxFile(crxPath, tampermonkeyDir);
    
    console.log(`扩展已解压到: ${tampermonkeyDir}`);
    
    // 验证解压是否成功
    if (!fs.existsSync(path.join(tampermonkeyDir, 'manifest.json'))) {
      throw new Error('解压后未找到manifest.json文件，解压可能失败');
    }
    
    // 读取manifest.json以获取扩展信息
    const manifestPath = path.join(tampermonkeyDir, 'manifest.json');
    const manifestData = fs.readFileSync(manifestPath, 'utf8');
    const manifest = JSON.parse(manifestData);
    console.log(`解压的扩展信息: ${manifest.name} v${manifest.version}`);
    
    // 加载解压后的扩展
    const extInfo = await sessionInstance.loadExtension(tampermonkeyDir, {
      allowFileAccess: true
    });
    
    console.log('✅ 从解压目录加载Tampermonkey扩展成功:', extInfo.id);
    
    // 存储扩展ID和路径
    store.set('tampermonkey_extension_id', extInfo.id);
    store.set('tampermonkey_extension_path', tampermonkeyDir);
    
    // 配置Tampermonkey扩展
    configureTampermonkey(sessionInstance, extInfo.id);
    
    return extInfo;
  } catch (error) {
    console.error('解压并加载扩展失败:', error);
    throw error;
  }
}

// 验证卡密 - 优化验证流程
async function verifyLicense(licenseKey) {
  try {
    console.log('验证卡密:', licenseKey);
    
    if (!licenseKey || licenseKey.trim() === '') {
      return {
        success: false,
        message: '卡密不能为空'
      };
    }

    // 静默处理空格：自动移除空格字符
    licenseKey = licenseKey.replace(/\s/g, '');
    
    // 获取保存的许可证信息用于离线验证
    const savedLicense = store.get('license');
    const savedShopInfo = store.get('shop_info');
    
    // 尝试主服务器验证
    try {
      const mainUrl = `${SERVER_CONFIG.main}/api/verify.php`;
      const deviceId = store.get('device_id') || uuidv4();
      
      console.log(`使用主服务器验证: ${mainUrl}`);
      
      // 【优化】增强API格式，添加设备信息用于同时登录检测
      const response = await makeRequest(mainUrl, {
        key: licenseKey,
        device_id: deviceId,
        version: app.getVersion() || '1.0.0',
        device_name: require('os').hostname() || 'Unknown',
        platform: process.platform || 'unknown',
        check_concurrent_login: 1 // 启用同时登录检测
      });
      
      if (response.data) {
        if (response.data.success) {
          console.log('卡密验证成功，保存卡密信息');
          
          // 成功验证后保存卡密信息
          updateShopInfoCache(response.data, licenseKey);
          
          // 设置全局缓存
          global.shopInfoCache = store.get('shop_info') || {};
          
          return {
            success: true,
            message: '卡密验证成功',
            shopInfo: global.shopInfoCache
          };
        } else {
          console.error('卡密验证失败:', response.data.message);

          // 【新增】处理同时登录限制错误
          let errorMessage = response.data.message || '卡密验证失败';

          if (response.data.error_code) {
            switch (response.data.error_code) {
              case 'CONCURRENT_LOGIN_LIMIT':
                // 单店卡密同时登录限制
                errorMessage = '老板您好！\n\n该卡密已经在其他电脑登录，不能同时登录';
                break;
              case 'MULTI_STORE_LOGIN_LIMIT':
                // 多店卡密同时登录限制
                const maxDevices = response.data.max_devices || '未知';
                errorMessage = `老板您好！\n\n该多店卡密已达到最大同时登录数量限制（${maxDevices}台设备），请先退出其他设备后再登录`;
                break;
              case 'KEY_DISABLED':
                errorMessage = '您的卡密已被禁用，请联系代理商';
                break;
              case 'KEY_EXPIRED':
                errorMessage = '您的卡密已过期，请联系代理商续费';
                break;
              case 'KEY_DELETED':
                errorMessage = '卡密不存在或已被删除，请检查卡密是否正确';
                break;
              default:
                errorMessage = response.data.message || '卡密验证失败';
            }
          }

          return {
            success: false,
            message: errorMessage,
            error_code: response.data.error_code
          };
        }
      }
    } catch (mainError) {
      console.error('主服务器验证失败:', mainError.message);
      
      // 尝试备用服务器验证
      try {
        const backupUrl = `${SERVER_CONFIG.backup}/api/verify.php`;
        const deviceId = store.get('device_id') || uuidv4();
        
        console.log(`使用备用服务器验证: ${backupUrl}`);
        
        // 【优化】增强API格式，添加设备信息用于同时登录检测
        const response = await makeRequest(backupUrl, {
          key: licenseKey,
          device_id: deviceId,
          version: app.getVersion() || '1.0.0',
          device_name: require('os').hostname() || 'Unknown',
          platform: process.platform || 'unknown',
          check_concurrent_login: 1 // 启用同时登录检测
        });
        
        if (response.data) {
          if (response.data.success) {
            console.log('备用服务器卡密验证成功，保存卡密信息');
            
            // 成功验证后保存卡密信息
            updateShopInfoCache(response.data, licenseKey);
            
            // 设置全局缓存
            global.shopInfoCache = store.get('shop_info') || {};
            
            return {
              success: true,
              message: '卡密验证成功',
              shopInfo: global.shopInfoCache
            };
          } else {
            console.error('备用服务器卡密验证失败:', response.data.message);

            // 【新增】处理同时登录限制错误
            let errorMessage = response.data.message || '卡密验证失败';

            if (response.data.error_code) {
              switch (response.data.error_code) {
                case 'CONCURRENT_LOGIN_LIMIT':
                  // 单店卡密同时登录限制
                  errorMessage = '老板您好！\n\n该卡密已经在其他电脑登录，不能同时登录';
                  break;
                case 'MULTI_STORE_LOGIN_LIMIT':
                  // 多店卡密同时登录限制
                  const maxDevices = response.data.max_devices || '未知';
                  errorMessage = `老板您好！\n\n该多店卡密已达到最大同时登录数量限制（${maxDevices}台设备），请先退出其他设备后再登录`;
                  break;
                case 'KEY_DISABLED':
                  errorMessage = '您的卡密已被禁用，请联系代理商';
                  break;
                case 'KEY_EXPIRED':
                  errorMessage = '您的卡密已过期，请联系代理商续费';
                  break;
                case 'KEY_DELETED':
                  errorMessage = '卡密不存在或已被删除，请检查卡密是否正确';
                  break;
                default:
                  errorMessage = response.data.message || '卡密验证失败';
              }
            }

            return {
              success: false,
              message: errorMessage,
              error_code: response.data.error_code
            };
          }
        }
      } catch (backupError) {
        console.error('备用服务器验证错误:', backupError);
        
        // 如果之前验证过，尝试使用离线缓存
        if (savedLicense && savedLicense === licenseKey && savedShopInfo) {
          console.log('使用离线缓存验证卡密');
          return {
            success: true,
            shopInfo: savedShopInfo,
            offline: true
          };
        }
        
        return {
          success: false,
          message: '网络错误，请检查网络连接'
        };
      }
    }
  } catch (error) {
    console.error('验证卡密错误:', error);
    return {
      success: false,
      message: '验证过程中发生错误，请重试'
    };
  }
}

// 新增：更新店铺信息缓存的辅助函数
function updateShopInfoCache(responseData, licenseKey) {
  // 保存卡密
  store.set('license', licenseKey);
  
  // 保存设备ID
  if (!store.get('device_id')) {
    store.set('device_id', uuidv4());
  }
  
  // 提取店铺信息
  const shopInfo = {
    license: licenseKey,
    shopId: responseData.wechat_store_id || responseData.store_id || 'unknown',
    shopName: responseData.store_name || '未知店铺',
    wechatStoreId: responseData.wechat_store_id || 'unknown',
    expireDate: responseData.expiry_date || null,
    hasCustomerService: responseData.has_customer_service || false,
    hasProductListing: responseData.has_product_listing || false,
    functionType: responseData.function_type || 'unknown'
  };
  
  // 处理多店铺情况
  if (responseData.stores && Array.isArray(responseData.stores) && responseData.stores.length > 0) {
    shopInfo.isMultiStore = true;
    shopInfo.shops = responseData.stores.map(store => ({
      id: store.wechat_store_id || store.store_id || 'unknown',
      name: store.store_name || '未知店铺',
      wechatStoreId: store.wechat_store_id || 'unknown'
    }));
  } else {
    shopInfo.isMultiStore = false;
    shopInfo.shops = [{
      id: shopInfo.shopId,
      name: shopInfo.shopName,
      wechatStoreId: shopInfo.wechatStoreId
    }];
  }
  
  // 保存店铺信息
  store.set('shop_info', shopInfo);
}

// 设置IPC处理程序
function setupIpcHandlers() {
  // 验证卡密
  ipcMain.handle('verify-license', async (event, licenseKey) => {
    try {
      const result = await verifyLicense(licenseKey);
      
      // 验证成功后检查卡密到期时间并创建主窗口
      if (result.success) {
        console.log('卡密验证成功，检查到期时间');
        
        // 检查卡密到期时间
        const shopInfo = result.shopInfo;
        if (shopInfo && shopInfo.expireDate) {
          const expireDate = new Date(shopInfo.expireDate);
          const now = new Date();
          const diffTime = expireDate.getTime() - now.getTime();
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          
          // 根据剩余天数和卡密类型决定是否显示提醒
          let shouldShowReminder = false;
          let reminderMessage = '';
          
          // 获取卡密类型（默认为月卡）
          const licenseType = shopInfo.licenseType || 'month';
          
          if (diffDays <= 0) {
            // 已过期
            shouldShowReminder = true;
            reminderMessage = '您的卡密已经过期\n如需继续使用请联系代理商续费';
          } else if (licenseType === 'year' && diffDays <= 30) {
            // 年卡最后一个月提醒
            shouldShowReminder = true;
            reminderMessage = `您的卡密还有${diffDays}天到期\n如需继续使用请联系代理商续费`;
          } else if (licenseType === 'month' && diffDays <= 7) {
            // 月卡最后7天提醒
            shouldShowReminder = true;
            reminderMessage = `您的卡密还有${diffDays}天到期\n如需继续使用请联系代理商续费`;
          } else if (licenseType === 'week') {
            // 周卡每天提醒
            shouldShowReminder = true;
            reminderMessage = `您的卡密还有${diffDays}天到期\n如需继续使用请联系代理商续费`;
          }
          
          if (shouldShowReminder) {
            // 显示到期提醒对话框
            dialog.showMessageBox(loginWindow, {
              type: 'warning',
              title: '卡密到期提醒',
              message: '卡密到期提醒',
              detail: reminderMessage,
              buttons: ['确认']
            }).then(() => {
              // 对话框关闭后创建主窗口
              // 设置标志，表示不是用户手动关闭窗口
              isQuitting = true;
              // 使用setTimeout避免阻塞UI
              setTimeout(() => {
                createMainWindow(result.shopInfo);
                // 重置标志
                isQuitting = false;
              }, 100);
            });
          } else {
            // 不需要提醒，直接创建主窗口
            // 设置标志，表示不是用户手动关闭窗口
            isQuitting = true;
            // 使用setTimeout避免阻塞UI
            setTimeout(() => {
              createMainWindow(result.shopInfo);
              // 重置标志
              isQuitting = false;
            }, 100);
          }
        } else {
          // 没有到期日期信息，直接创建主窗口
          console.log('卡密验证成功，创建主窗口');
          // 设置标志，表示不是用户手动关闭窗口
          isQuitting = true;
          // 使用setTimeout避免阻塞UI
          setTimeout(() => {
            createMainWindow(result.shopInfo);
            // 重置标志
            isQuitting = false;
          }, 100);
        }
      } else {
        console.error('卡密验证失败:', result.message);
      }
      
      return result;
    } catch (error) {
      console.error('验证卡密失败:', error);
      return { success: false, message: error.message || '验证失败，请重试' };
    }
  });
  
  // 获取店铺信息
  ipcMain.handle('get-shop-info', () => {
    return global.shopInfoCache || {};
  });
  
  // 获取许可证密钥
  ipcMain.handle('get-license-key', () => {
    return store.get('license') || '';
  });

  // 【新增】处理剪贴板粘贴请求
  ipcMain.handle('get-clipboard-text', async () => {
    try {
      const { clipboard } = require('electron');
      const text = clipboard.readText();
      console.log('从剪贴板获取文本:', text ? `${text.length}个字符` : '空');
      return text || '';
    } catch (error) {
      console.error('获取剪贴板内容失败:', error);
      return '';
    }
  });

  // 获取版本信息
  ipcMain.handle('get-version-info', () => {
    return versionManager.getAllVersionInfo();
  });

  // 获取架构信息
  ipcMain.handle('get-architecture-info', () => {
    return architectureDetector.generateDiagnosticReport();
  });

  // 获取协议内容
  ipcMain.handle('get-agreement', async (event, type = 'privacy') => {
    try {
      if (appSettingsAPI) {
        const result = await appSettingsAPI.getAgreement(type);
        return result;
      } else {
        return { success: false, message: 'APP设置API未初始化' };
      }
    } catch (error) {
      console.error('获取协议失败:', error);
      return { success: false, message: error.message };
    }
  });

  // 获取所有已发布的协议列表
  ipcMain.handle('get-published-agreements', async () => {
    try {
      if (appSettingsAPI) {
        const result = await appSettingsAPI.getPublishedAgreements();
        return result;
      } else {
        return { success: false, message: 'APP设置API未初始化' };
      }
    } catch (error) {
      console.error('获取协议列表失败:', error);
      return { success: false, message: error.message };
    }
  });

  // 打开协议独立窗口
  ipcMain.handle('open-agreement-window', async (event, agreement) => {
    try {
      if (agreementManager) {
        await agreementManager.showAgreementWindow(agreement);
        return { success: true };
      } else {
        return { success: false, message: '协议管理器未初始化' };
      }
    } catch (error) {
      console.error('打开协议窗口失败:', error);
      return { success: false, message: error.message };
    }
  });


  
  // 【新增】移除店铺
  ipcMain.handle('remove-shop', async (event, shopId) => {
    try {
      // 获取当前店铺信息
      const shopInfo = global.shopInfoCache || store.get('shop_info') || {};
      
      // 如果没有多店铺，则不允许移除
      if (!shopInfo.isMultiStore || !shopInfo.shops || shopInfo.shops.length <= 1) {
        return { success: false, message: '不能移除唯一的店铺' };
      }
      
      // 从店铺列表中移除指定店铺
      const updatedShops = shopInfo.shops.filter(shop => shop.id !== shopId);
      
      // 如果移除后没有店铺了，则不允许移除
      if (updatedShops.length === 0) {
        return { success: false, message: '不能移除所有店铺' };
      }
      
      // 更新店铺信息
      shopInfo.shops = updatedShops;
      
      // 如果移除的是当前店铺，则切换到第一个店铺
      if (shopInfo.shopId === shopId) {
        shopInfo.shopId = updatedShops[0].id;
        shopInfo.shopName = updatedShops[0].name;
        shopInfo.wechatStoreId = updatedShops[0].wechatStoreId;
      }
      
      // 保存更新后的店铺信息
      store.set('shop_info', shopInfo);
      global.shopInfoCache = shopInfo;
      
      return { success: true, message: '店铺已移除', shopInfo };
    } catch (error) {
      console.error('移除店铺失败:', error);
      return { success: false, message: error.message || '移除店铺失败' };
    }
  });
  
  // 【新增】打开添加店铺对话框
  ipcMain.handle('open-add-shop-dialog', async (event) => {
    try {
      // 获取许可证密钥
      const licenseKey = store.get('license');
      if (!licenseKey) {
        return { success: false, message: '未找到许可证密钥' };
      }
      
      // 创建添加店铺对话框
      const addShopWindow = new BrowserWindow({
        width: 500,
        height: 400,
        parent: mainWindow,
        modal: true,
        show: false,
        resizable: false,
        minimizable: false,
        maximizable: false,
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true,
          preload: path.join(__dirname, 'preload.js')
        }
      });
      
      // 加载添加店铺页面
      await addShopWindow.loadURL(`https://xiaomeihuakefu.cn/add-shop.html?key=${encodeURIComponent(licenseKey)}`);
      
      // 显示窗口
      addShopWindow.show();
      
      // 监听窗口关闭
      addShopWindow.on('closed', () => {
        // 重新验证卡密，获取最新的店铺信息
        verifyLicense(licenseKey).then(result => {
          if (result.success) {
            // 更新店铺信息
            global.shopInfoCache = result.shopInfo;
            store.set('shop_info', result.shopInfo);
            
            // 通知渲染进程更新店铺列表
            if (mainWindow && !mainWindow.isDestroyed()) {
              mainWindow.webContents.send('update-shop-info', result.shopInfo);
            }
          }
        }).catch(err => {
          console.error('重新验证卡密失败:', err);
        });
      });
      
      return { success: true };
    } catch (error) {
      console.error('打开添加店铺对话框失败:', error);
      return { success: false, message: error.message || '打开添加店铺对话框失败' };
    }
  });
  
  // 【修复】打开店铺浏览器 - 不再创建新窗口，而是在主窗口中创建标签页
  ipcMain.handle('open-shop-browser', (event, shopInfo) => {
    if (!mainWindow || mainWindow.isDestroyed()) {
      return { success: false, message: '主窗口未创建' };
    }
    if (!shopInfo || !shopInfo.url) {
      return { success: false, message: '店铺信息不完整' };
    }
    
    try {
      console.log(`命令主窗口创建新标签页: ${shopInfo.shopName} (${shopInfo.shopId})`);
      mainWindow.webContents.send('create-tab', {
        url: shopInfo.url,
        title: shopInfo.shopName || '新店铺'
      });
      return { success: true };
    } catch (error) {
      console.error('在主窗口中创建店铺标签页失败:', error);
      return { success: false, message: error.message };
    }
  });
  
  // 创建新标签页
  ipcMain.handle('create-new-tab', (event, { url, title }) => {
    if (!mainWindow || mainWindow.isDestroyed()) {
      return { success: false, message: '主窗口未创建' };
    }
    
    mainWindow.webContents.send('create-tab', { url, title });
    return { success: true };
  });
  
  // 退出登录
  ipcMain.handle('logout', async () => {
    try {
      console.log('用户请求退出登录');

      // 【新增】保存当前卡密，以便退出登录后自动填充
      const currentLicense = store.get('license');
      if (currentLicense) {
        store.set('logout_saved_license', currentLicense);
        console.log('已保存当前卡密，退出登录后将自动填充');
      }

      // 设置一个标志，表示这是用户主动退出登录，不应该自动验证
      store.set('manual_logout', true);

      // 【关键修复】设置退出登录标记，确保下次启动时不会自动恢复登录状态
      store.set('logout_detected', true);
      store.set('logout_timestamp', Date.now());

      // 清除Cookie管理器中的登录状态
      await cookieManager.clearLoginState();

      // 【关键修复】完全清理微信小店登录状态，包括保存的状态文件
      await cookieManager.resetWeixinStoreLoginState();

      // 【新增】专门清理微信小店会话状态，解决重定向问题
      await cookieManager.clearWeixinStoreSession();

      // 清除所有Cookie
      await cookieManager.clearAllCookies();

      // 清除存储的卡密和店铺信息
      store.delete('license');
      store.delete('shop_info');

      // 【增强】清除所有相关的存储项
      const allKeys = store.store;
      Object.keys(allKeys).forEach(key => {
        if (key.includes('shop') ||
            key.includes('store') ||
            key.includes('xmh_') ||
            key.includes('xiaomeihua') ||
            key.includes('login') ||
            key.includes('session') ||
            key.includes('cookie') ||
            key.includes('auth') ||
            key.includes('token') ||
            key.includes('page_exit') ||
            key.includes('detection')) {
          store.delete(key);
          console.log('已清除存储项: ' + key);
        }
      });

      // 清除全局缓存
      global.shopInfoCache = null;

      // 【增强】清除所有全局状态
      global.currentShopId = null;
      global.selectedShopName = null;
      global.shopDetectionCompleted = false;
      global.currentShopCheckStatus = null;

      // 清除所有保存的Cookie文件
      try {
        const userDataPath = app.getPath('userData');
        const files = fs.readdirSync(userDataPath);

        // 删除所有cookie-*.txt文件
        files.filter(file => file.startsWith('cookie-') && file.endsWith('.txt'))
             .forEach(file => {
               try {
                 fs.unlinkSync(path.join(userDataPath, file));
                 console.log(`已删除Cookie文件: ${file}`);
               } catch (err) {
                 console.error(`删除Cookie文件失败: ${file}`, err);
               }
             });

        // 删除其他会话相关文件
        const sessionFiles = ['session-data.json', 'cookies-data.json', 'local-storage-data.json', 'document-cookies.txt'];
        sessionFiles.forEach(file => {
          const filePath = path.join(userDataPath, file);
          if (fs.existsSync(filePath)) {
            try {
              fs.unlinkSync(filePath);
              console.log(`已删除会话文件: ${file}`);
            } catch (err) {
              console.error(`删除会话文件失败: ${file}`, err);
            }
          }
        });
      } catch (err) {
        console.error('清理会话文件失败:', err);
      }

      // 创建登录窗口（如果不存在）
      if (!loginWindow || loginWindow.isDestroyed()) {
        createLoginWindow();
      }

      // 隐藏主窗口而不是关闭它
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.hide();
      }

      console.log('退出登录完成，所有登录状态已清除');
      return { success: true };
    } catch (error) {
      console.error('退出登录失败:', error);
      return { success: false, error: error.message };
    }
  });

  // 【新增】验证退出登录状态
  ipcMain.handle('verify-logout-state', async () => {
    try {
      console.log('🔍 验证退出登录状态...');

      // 检查是否还有残留的登录状态
      const hasLoginState = await cookieManager.checkValidWeixinLoginState();

      if (hasLoginState) {
        console.log('⚠️ 检测到残留的登录状态，执行补充清理...');

        // 执行补充清理
        await cookieManager.resetWeixinStoreLoginState();
        await cookieManager.clearWeixinStoreSession();

        console.log('✅ 补充清理完成');
        return { success: true, hadResidue: true };
      } else {
        console.log('✅ 退出登录状态验证通过，无残留状态');
        return { success: true, hadResidue: false };
      }
    } catch (error) {
      console.error('❌ 验证退出登录状态失败:', error);
      return { success: false, error: error.message };
    }
  });

  // 【新增】更新窗口标题
  ipcMain.handle('update-window-title', async (event, title) => {
    try {
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.setTitle(title);
        console.log(`窗口标题已更新: ${title}`);
        return { success: true };
      } else {
        console.warn('主窗口不存在或已销毁，无法更新标题');
        return { success: false, message: '主窗口不存在' };
      }
    } catch (error) {
      console.error('更新窗口标题失败:', error);
      return { success: false, error: error.message };
    }
  });
  
  // 处理新窗口请求
  ipcMain.on('new-window-request', (event, data) => {
    // data 应该是 { url, title }
    if (!data || !data.url) {
      console.error('[MAIN] 收到了无效的 new-window-request 数据:', data);
      return;
    }

    console.log(`[STEP 4/5] Main Process(main.js) 收到 new-window-request, URL: ${data.url}`);

    if (mainWindow && !mainWindow.isDestroyed()) {
        console.log(`[STEP 5/5] Main Process(main.js) 正在向主窗口发送 'create-tab' 事件`);
        mainWindow.webContents.send('create-tab', {
            url: data.url,
            title: data.title || '新页面'
        });
    } else {
        console.error('[MAIN] 无法处理新窗口请求，因为主窗口不存在。');
        // 作为备用方案，在外部浏览器中打开
        shell.openExternal(data.url).catch(err => {
            console.error(`[MAIN] 备用方案（打开外部浏览器）也失败了: ${err.message}`);
        });
    }
  });
  
  // 窗口控制 - 关闭窗口
  ipcMain.on('close-window', (event) => {
    const win = BrowserWindow.fromWebContents(event.sender);
    if (win && !win.isDestroyed()) {
      win.close();
    }
  });
  
  // 窗口控制 - 最小化窗口
  ipcMain.on('minimize-window', (event) => {
    const win = BrowserWindow.fromWebContents(event.sender);
    if (win && !win.isDestroyed()) {
      win.minimize();
    }
  });

  // 窗口控制 - 最大化/还原窗口
  ipcMain.on('maximize-window', (event) => {
    const win = BrowserWindow.fromWebContents(event.sender);
    if (win && !win.isDestroyed()) {
      if (win.isMaximized()) {
        win.unmaximize();
      } else {
        win.maximize();
      }
    }
  });
  
  // 获取缓存的店铺信息
  ipcMain.on('get-cached-shop-info', (event) => {
    event.returnValue = global.shopInfoCache || {};
  });

  // 【新增】确保Cookie管理器知道新的session分区
  ipcMain.handle('ensure-shop-session', async (event, shopId) => {
    try {
      if (!shopId) {
        return { success: false, message: '店铺ID不能为空' };
      }

      const partition = `persist:shop_${shopId}`;
      console.log(`🔧 确保店铺session分区存在: ${partition}`);

      // 让Cookie管理器创建或获取这个session分区
      const sessionInstance = cookieManager.getOrCreateSession(partition);

      if (sessionInstance) {
        console.log(`✅ 店铺session分区已就绪: ${partition}`);
        return { success: true, partition };
      } else {
        console.error(`❌ 创建店铺session分区失败: ${partition}`);
        return { success: false, message: '创建session分区失败' };
      }
    } catch (error) {
      console.error('确保店铺session分区失败:', error);
      return { success: false, message: error.message };
    }
  });
  
  // 获取xinkami.js脚本内容
  ipcMain.handle('get-xinkami-script', async (event) => {
    try {
      console.log('收到获取xinkami.js脚本请求');
      
      // 获取xinkami.js脚本
      const { getXinkamiScript } = require('./kamiyan-injector');
      const scriptContent = getXinkamiScript();
      
      if (!scriptContent) {
        console.error('无法获取xinkami.js脚本');
        return null;
      }
      
      console.log(`成功获取xinkami.js脚本，大小: ${scriptContent.length} 字节`);
      return scriptContent;
    } catch (error) {
      console.error('获取xinkami.js脚本失败:', error);
      return null;
    }
  });
  
  // 【最终修复】添加IPC handle，用于安全地获取preload-browser.js的绝对路径
  ipcMain.handle('get-preload-browser-path', () => {
    const isDev = !app.isPackaged;
    let preloadPath;

    if (isDev) {
      // 开发环境：路径相对于项目根目录
      preloadPath = path.join(__dirname, 'preload-browser.js');
    } else {
      // 生产环境：路径在asar解包后的目录中
      preloadPath = path.join(process.resourcesPath, 'app.asar.unpacked', 'src', 'preload-browser.js');
    }
    
    // 增加文件存在性检查，确保路径有效
    if (fs.existsSync(preloadPath)) {
      console.log(`[Main Process] 找到preload脚本: ${preloadPath}`);
      return preloadPath;
    } else {
      console.error(`[Main Process] preload脚本未找到，路径: ${preloadPath}`);
      // 尝试备用路径 (针对某些打包结构)
      const alternatePath = path.join(app.getAppPath(), 'src', 'preload-browser.js');
      if (fs.existsSync(alternatePath)) {
        console.log(`[Main Process] 在备用路径找到preload脚本: ${alternatePath}`);
        return alternatePath;
      }
      console.error(`[Main Process] 备用路径也未找到: ${alternatePath}`);
      return null;
    }
  });
  
  // 【全新修复方案】添加IPC handle，用于直接读取并返回 preload-browser.js 的文件内容
  ipcMain.handle('get-preload-script-content', () => {
    const isDev = !app.isPackaged;
    let preloadPath;

    if (isDev) {
      preloadPath = path.join(__dirname, 'preload-browser.js');
    } else {
      preloadPath = path.join(process.resourcesPath, 'app.asar.unpacked', 'src', 'preload-browser.js');
    }

    try {
      if (fs.existsSync(preloadPath)) {
        console.log(`[Main Process] 读取 preload 脚本内容从: ${preloadPath}`);
        return fs.readFileSync(preloadPath, 'utf-8');
      } else {
        console.error(`[Main Process] preload 脚本文件未找到: ${preloadPath}`);
        // 尝试备用路径
        const alternatePath = path.join(app.getAppPath(), 'src', 'preload-browser.js');
        if (fs.existsSync(alternatePath)) {
           console.log(`[Main Process] 从备用路径读取 preload 脚本内容: ${alternatePath}`);
           return fs.readFileSync(alternatePath, 'utf-8');
        }
        console.error(`[Main Process] 备用路径的 preload 脚本也未找到: ${alternatePath}`);
        return null;
      }
    } catch (error) {
        console.error(`[Main Process] 读取 preload 脚本内容失败:`, error);
        return null;
    }
  });
  
  // 保存会话状态
  ipcMain.on('save-session-state', async (event) => {
    try {
      console.log('收到保存会话状态请求');
      
      // 获取发送请求的窗口
      const win = BrowserWindow.fromWebContents(event.sender);
      if (win && !win.isDestroyed()) {
        // 获取窗口URL
        const url = win.webContents.getURL();
        
        // 如果是微信相关页面，执行特殊处理
        if (url && (url.includes('store.weixin.qq.com') ||
                   url.includes('filehelper.weixin.qq.com') ||
                   url.includes('channels.weixin.qq.com') ||
                   url.includes('weixin.qq.com'))) {
          console.log('检测到微信商店窗口，执行特殊会话保存...');
          
          // 执行脚本获取页面状态
          try {
            const pageState = await win.webContents.executeJavaScript(`
              (function() {
                try {
                  // 收集页面状态信息
                  const state = {
                    url: window.location.href,
                    title: document.title,
                    isLoggedIn: document.cookie.includes('wxticket') || document.cookie.includes('login_ticket'),
                    cookies: document.cookie,
                    localStorage: {},
                    timestamp: Date.now()
                  };
                  
                  // 收集localStorage
                  for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    try {
                      state.localStorage[key] = localStorage.getItem(key);
                    } catch (e) {
                      console.error('读取localStorage项失败:', e);
                    }
                  }
                  
                  return state;
                } catch (e) {
                  return { error: e.toString() };
                }
              })();
            `);
            
            if (pageState && !pageState.error) {
              console.log('成功获取页面状态，保存到文件');
              fs.writeFileSync(SESSION_DATA_PATH, JSON.stringify(pageState, null, 2));
              
              // 立即保存Cookie
              await saveSessionState();
              
              event.reply('save-session-state-reply', { success: true });
              return;
            }
          } catch (err) {
            console.error('获取页面状态失败:', err);
          }
        }
      }
      
      // 如果上面的特殊处理失败或不适用，执行常规的会话状态保存
      await saveSessionState();
      event.reply('save-session-state-reply', { success: true });
    } catch (error) {
      console.error('处理保存会话状态请求失败:', error);
      event.reply('save-session-state-reply', { success: false, error: error.message });
    }
  });
  
  // 保存单个Cookie
  ipcMain.on('save-cookie', async (event, { name, value }) => {
    try {
      console.log(`收到保存Cookie请求: ${name}=${value.substring(0, 10)}...`);
      
      // 保存到文件
      const cookieFilePath = path.join(app.getPath('userData'), `cookie-${name}.txt`);
      fs.writeFileSync(cookieFilePath, `${name}=${value}`);
      
      // 设置Cookie到所有相关域名
      for (const domain of ['store.weixin.qq.com', 'filehelper.weixin.qq.com', 'channels.weixin.qq.com', 'weixin.qq.com', 'wx.qq.com']) {
        try {
          await session.defaultSession.cookies.set({
            url: `https://${domain}/`,
            name: name,
            value: value,
            domain: domain,
            path: '/',
            secure: true,
            httpOnly: true,
            expirationDate: Math.floor(Date.now() / 1000) + 86400 * 365, // 一年
            sameSite: 'no_restriction'
          });
          
          console.log(`成功设置Cookie ${name} 到域名 ${domain}`);
        } catch (err) {
          console.error(`设置Cookie ${name} 到域名 ${domain} 失败:`, err);
        }
      }
      
      // 立即保存会话状态
      await saveSessionState();
      
      event.reply('save-cookie-reply', { success: true });
    } catch (error) {
      console.error('处理保存Cookie请求失败:', error);
      event.reply('save-cookie-reply', { success: false, error: error.message });
    }
  });
  
  // 恢复关键Cookie
  ipcMain.on('restore-key-cookies', async (event) => {
    try {
      console.log('收到恢复关键Cookie请求');

      // 使用新的Cookie管理器恢复Cookie
      await cookieManager.restoreAllCookies();

      event.reply('restore-key-cookies-reply', { success: true });
    } catch (error) {
      console.error('处理恢复关键Cookie请求失败:', error);
      event.reply('restore-key-cookies-reply', { success: false, error: error.message });
    }
  });
  
  // 处理刷新Cookie请求
  ipcMain.on('refresh-cookies', async (event) => {
    try {
      console.log('收到刷新Cookie请求');
      await cookieManager.refreshCookies();
      event.reply('refresh-cookies-reply', { success: true });
    } catch (error) {
      console.error('处理刷新Cookie请求失败:', error);
      event.reply('refresh-cookies-reply', { success: false, error: error.message });
    }
  });

  // 【增强】处理页面退出检测事件
  ipcMain.on('page-exit-detected', async (event, exitData) => {
    try {
      console.log('🚪 收到页面退出检测事件:', exitData);

      // 【新增】如果是退出登录，立即清理所有状态
      if (exitData.isLogoutExit) {
        console.log('🔄 检测到退出登录，执行完整清理...');

        // 设置退出登录标记
        store.set('logout_detected', true);
        store.set('logout_timestamp', Date.now());

        // 【关键修复】完全重置微信小店的session分区
        await resetWeixinStoreSessionPartition(exitData);

        // 立即执行完整的登录状态清理
        await handlePageExitCleanup(exitData);

        // 完全重置所有webview
        if (mainWindow && !mainWindow.isDestroyed()) {
          mainWindow.webContents.send('reset-all-webviews');
        }
      } else {
        // 普通页面退出
        await handlePageExitCleanup(exitData);
      }

    } catch (error) {
      console.error('处理页面退出检测失败:', error);
    }
  });

  // 【新增】完全重置微信小店的session分区
  async function resetWeixinStoreSessionPartition(exitData) {
    console.log('🔄 完全重置微信小店session分区...');

    try {
      // 1. 获取当前的微信小店session分区
      const weixinPartitions = [];
      for (const [partition, sessionInstance] of cookieManager.sessionInstances) {
        if (partition.includes('shop_') || partition.includes('weixin')) {
          weixinPartitions.push({ partition, sessionInstance });
        }
      }

      // 2. 完全清理每个微信小店session分区
      for (const { partition, sessionInstance } of weixinPartitions) {
        console.log(`🗑️ 重置session分区: ${partition}`);

        try {
          // 清理所有Cookie
          await sessionInstance.clearStorageData({
            storages: ['cookies', 'localstorage', 'sessionstorage', 'websql', 'indexdb', 'cachestorage']
          });

          // 清理HTTP缓存
          await sessionInstance.clearCache();

          // 清理所有微信域名的Cookie
          const weixinDomains = [
            'store.weixin.qq.com',
            'filehelper.weixin.qq.com',
            'channels.weixin.qq.com',
            'weixin.qq.com',
            'wx.qq.com',
            'mp.weixin.qq.com'
          ];

          for (const domain of weixinDomains) {
            try {
              const cookies = await sessionInstance.cookies.get({ domain: domain });
              for (const cookie of cookies) {
                const url = cookie.secure ? `https://${cookie.domain}` : `http://${cookie.domain}`;
                await sessionInstance.cookies.remove(url, cookie.name);
              }
            } catch (err) {
              console.log(`⚠️ 清理域名 ${domain} 的Cookie失败:`, err.message);
            }
          }

          console.log(`✅ session分区 ${partition} 重置完成`);
        } catch (err) {
          console.error(`❌ 重置session分区 ${partition} 失败:`, err);
        }
      }

      // 3. 从Cookie管理器中移除这些session分区，强制重新创建
      for (const { partition } of weixinPartitions) {
        cookieManager.sessionInstances.delete(partition);
        console.log(`🗑️ 已从Cookie管理器中移除session分区: ${partition}`);
      }

      // 4. 清理session分区的物理文件
      const { app } = require('electron');
      const path = require('path');
      const fs = require('fs');

      try {
        const userDataPath = app.getPath('userData');
        const partitionsPath = path.join(userDataPath, 'Partitions');

        if (fs.existsSync(partitionsPath)) {
          const partitionDirs = fs.readdirSync(partitionsPath);
          for (const dir of partitionDirs) {
            if (dir.includes('shop_') || dir.includes('weixin')) {
              const partitionPath = path.join(partitionsPath, dir);
              try {
                fs.rmSync(partitionPath, { recursive: true, force: true });
                console.log(`🗑️ 已删除session分区目录: ${dir}`);
              } catch (err) {
                console.log(`⚠️ 删除session分区目录失败: ${dir}`, err.message);
              }
            }
          }
        }
      } catch (err) {
        console.log('⚠️ 清理session分区物理文件失败:', err.message);
      }

      // 5. 重新创建干净的session分区
      await cookieManager.recreateWeixinStoreSessionPartitions();

      console.log('✅ 微信小店session分区完全重置完成');
    } catch (error) {
      console.error('❌ 重置微信小店session分区失败:', error);
    }
  }

  // 【增强】处理店铺检测触发事件
  ipcMain.on('trigger-shop-detection', async (event, detectionData) => {
    try {
      console.log('🏪 收到店铺检测触发事件:', detectionData);

      // 【新增】检查是否是新登录
      if (detectionData.isNewLogin) {
        console.log('🆕 检测到新登录，执行完整清理和重置...');

        // 清除退出登录标记
        store.delete('logout_detected');
        store.delete('logout_timestamp');

        // 清除页面退出标记
        store.delete('page_exited');
        store.delete('page_exit_timestamp');
        store.delete('page_exit_event');

        // 【新增】清除最近登录时间记录（如果存在的话）
        store.delete('recent_login_time');

        // 【新增】设置新的登录时间标记
        store.set('last_login_time', Date.now());
        console.log('✅ 已设置新的登录时间标记');
      }

      // 清除之前的店铺检测状态，强制重新检测
      await clearShopDetectionState();

      // 通知渲染进程执行店铺检测
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('force-shop-detection', {
          ...detectionData,
          forceDetection: true,
          clearPreviousState: true,
          isNewLogin: detectionData.isNewLogin || false
        });
        console.log('✅ 已通知渲染进程执行强制店铺检测');
      }

    } catch (error) {
      console.error('处理店铺检测触发失败:', error);
    }
  });

  // 处理登录状态变化通知
  ipcMain.on('login-state-changed', async (event, isLoggedIn) => {
    try {
      console.log('收到登录状态变化通知:', isLoggedIn);

      if (isLoggedIn) {
        // 登录成功，立即保存完整状态
        await cookieManager.saveCompleteLoginState();

        // 延迟再次保存，确保所有数据都已加载
        setTimeout(async () => {
          try {
            await cookieManager.saveCompleteLoginState();
            await saveSessionState();
            console.log('延迟保存登录状态完成');
          } catch (err) {
            console.error('延迟保存登录状态失败:', err);
          }
        }, 5000);
      }

      event.reply('login-state-changed-reply', { success: true });
    } catch (error) {
      console.error('处理登录状态变化失败:', error);
      event.reply('login-state-changed-reply', { success: false, error: error.message });
    }
  });

  // 【优化】处理微信小店重定向错误
  ipcMain.handle('handle-weixin-redirect-error', async (event, errorData) => {
    try {
      console.log('🚨 收到微信小店重定向错误:', errorData);

      // 标记错误类型，用于下次启动时判断
      if (errorData.errorType === 'redirect_error' || errorData.errorType === 'critical_redirect_error') {
        store.set('weixin_redirect_error', true);
        store.set('weixin_redirect_error_time', Date.now());

        // 【关键修复】对于严重重定向错误，立即执行完全重置
        if (errorData.errorType === 'critical_redirect_error') {
          console.log('🚨 检测到严重重定向错误，执行紧急完全重置...');

          // 立即执行session分区完全重置
          await resetWeixinStoreSessionPartition(errorData);

          // 设置强制退出登录标记
          store.set('logout_detected', true);
          store.set('logout_timestamp', Date.now());
        }
      } else if (errorData.errorType === 'login_failed') {
        store.set('weixin_login_error', true);
        store.set('weixin_login_error_time', Date.now());
      }

      // 重置微信小店登录状态
      await cookieManager.resetWeixinStoreLoginState();

      // 等待一段时间确保重置完成
      await new Promise(resolve => setTimeout(resolve, 2000));

      console.log('✅ 微信小店重定向错误处理完成');
      return { success: true };
    } catch (error) {
      console.error('❌ 处理微信小店重定向错误失败:', error);
      return { success: false, error: error.message };
    }
  });

  // 【新增】检查微信小店状态
  ipcMain.handle('check-weixin-store-status', async (event) => {
    try {
      console.log('🔍 检查微信小店状态...');

      const currentTime = Date.now();
      const lastShutdownTime = store.get('last_shutdown_time');
      const lastStartupTime = store.get('last_startup_time');
      const logoutDetected = store.get('logout_detected');

      const status = {
        needsClear: false,
        reason: '',
        lastShutdownTime,
        lastStartupTime,
        logoutDetected,
        currentTime
      };

      // 检查是否需要清理状态
      if (logoutDetected) {
        status.needsClear = true;
        status.reason = '检测到退出登录状态';
      } else if (!lastShutdownTime && lastStartupTime) {
        status.needsClear = true;
        status.reason = '检测到异常关闭';
      } else if (lastShutdownTime && (currentTime - lastShutdownTime) < 300000) {
        status.needsClear = true;
        status.reason = '检测到短时间内重启';
      }

      console.log('✅ 微信小店状态检查完成:', status);
      return { success: true, status };
    } catch (error) {
      console.error('❌ 检查微信小店状态失败:', error);
      return { success: false, error: error.message };
    }
  });

  // 【新增】处理AI知识库访问请求
  ipcMain.handle('access-ai-knowledge', async (event, options = {}) => {
    try {
      console.log('🤖 收到AI知识库访问请求:', options);

      const licenseKey = store.get('license');
      if (!licenseKey) {
        return { success: false, message: '未找到许可证密钥' };
      }

      // 获取店铺信息
      const shopInfo = store.get('shop_info') || {};

      // 创建AI知识库窗口
      const aiKnowledgeWindow = new BrowserWindow({
        width: options.width || 1200,
        height: options.height || 800,
        show: false,
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true,
          enableRemoteModule: false,
          webSecurity: true,
          allowRunningInsecureContent: false,
          experimentalFeatures: false,
          preload: path.join(__dirname, 'preload.js')
        }
      });

      // 设置User-Agent以便网站识别APP访问
      const session = aiKnowledgeWindow.webContents.session;
      session.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Electron/28.0.0 Safari/537.36 xiaomeihua-app/1.0.0');

      // 监听页面加载完成
      aiKnowledgeWindow.webContents.once('did-finish-load', async () => {
        console.log('✅ AI知识库页面加载完成');

        try {
          // 注入AI知识库专用脚本
          const { getAIKnowledgeScript } = require('./kamiyan-injector');
          const aiScript = getAIKnowledgeScript();

          const injectionScript = `
            // 设置全局变量
            window.xiaomeihuaLicenseKey = "${licenseKey}";
            window.xiaomeihuaShopInfo = ${JSON.stringify(shopInfo)};
            window.isXiaomeihuaApp = true;

            // 注入AI知识库脚本
            ${aiScript}

            // 增强页面功能而不清空内容
            console.log('🤖 AI知识库页面增强功能已注入');

            // 等待页面加载完成后通知
            if (document.readyState === 'complete') {
              if (window.aiKnowledgeAPI && typeof window.aiKnowledgeAPI.addKnowledgeItem === 'function') {
                setTimeout(() => {
                  window.aiKnowledgeAPI.addKnowledgeItem(
                    '🎯 独立窗口模式',
                    '通过独立窗口访问AI知识库，享受完整的增强功能体验。',
                    '#6f42c1'
                  );
                }, 1000);
              }
            }
          `;

          await aiKnowledgeWindow.webContents.executeJavaScript(injectionScript);
          console.log('✅ AI知识库脚本注入完成');

          // 显示窗口
          aiKnowledgeWindow.show();

        } catch (error) {
          console.error('❌ AI知识库脚本注入失败:', error);
        }
      });

      // 加载AI知识库页面
      const aiKnowledgeUrl = options.url || 'https://xiaomeihuakefu.cn/ai-knowledge.html';
      aiKnowledgeWindow.loadURL(aiKnowledgeUrl);

      console.log('✅ AI知识库窗口创建完成');
      return { success: true, message: 'AI知识库访问成功' };

    } catch (error) {
      console.error('❌ 处理AI知识库访问失败:', error);
      return { success: false, message: error.message };
    }
  });

  // 处理强制保存登录状态请求
  ipcMain.on('force-save-login-state', async (event) => {
    try {
      console.log('收到强制保存登录状态请求');

      // 保存完整登录状态
      await cookieManager.saveCompleteLoginState();

      // 保存会话状态
      await saveSessionState();

      // 强制保存Cookie
      await cookieManager.forceSaveCookies();

      console.log('强制保存登录状态完成');
      event.reply('force-save-login-state-reply', { success: true });
    } catch (error) {
      console.error('强制保存登录状态失败:', error);
      event.reply('force-save-login-state-reply', { success: false, error: error.message });
    }
  });

  // 新增：处理外部链接打开
  ipcMain.handle('open-external', async (event, url) => {
    try {
      console.log('打开外部链接:', url);
      await shell.openExternal(url);
      return true;
    } catch (error) {
      console.error('打开外部链接失败:', error);
      return false;
    }
  });

  // 注意：自动更新相关的基础IPC处理现在由AppUpdater类自己处理
  // 这里保留与主应用逻辑相关的处理

  ipcMain.on('cancel-update', () => {
    // 取消更新后，启动弹窗管理器（如果还没启动的话）
    if (popupManager && !popupManager.isStarted()) {
      console.log('🚀 取消更新，启动弹窗管理器');
      popupManager.start({ clearHistory: false });
    }
  });

  ipcMain.on('close-update-window', () => {
    // 更新窗口关闭后，启动弹窗管理器（如果还没启动的话）
    if (popupManager && !popupManager.isStarted()) {
      console.log('🚀 更新窗口关闭，启动弹窗管理器');
      popupManager.start({ clearHistory: false });
    }

    // 如果没有主窗口且登录窗口也不存在，则创建登录窗口
    if (!mainWindow && !loginWindow) {
      createLoginWindow();
    }
  });

  // 检查更新
  ipcMain.handle('check-for-updates', async () => {
    try {
      if (appUpdater) {
        const hasUpdate = await appUpdater.checkForUpdates();
        return { success: true, hasUpdate };
      }
      return { success: false, message: '更新管理器未初始化' };
    } catch (error) {
      console.error('检查更新失败:', error);
      return { success: false, message: error.message };
    }
  });






}

// 检查页面是否需要刷新以应用Cookie
async function checkIfNeedsRefresh(win) {
  try {
    if (win.isDestroyed()) return false;

    const needsRefresh = await win.webContents.executeJavaScript(`
      (function() {
        try {
          // 检查是否有登录相关的元素显示需要登录
          const loginElements = [
            document.querySelector('.login-container'),
            document.querySelector('.qr-code-login'),
            document.querySelector('.login-qr'),
            document.querySelector('.scan-login'),
            document.querySelector('.login-page'),
            document.querySelector('.qrcode-login'),
            document.querySelector('[class*="login"]'),
            document.querySelector('[id*="login"]')
          ];

          const hasLoginElement = loginElements.some(el => el && el.offsetParent !== null);

          // 检查Cookie是否存在
          const hasCookie = document.cookie.includes('wxticket') ||
                           document.cookie.includes('login_ticket') ||
                           document.cookie.includes('session') ||
                           document.cookie.includes('token') ||
                           document.cookie.includes('auth');

          // 如果有Cookie但页面仍显示登录界面，则需要刷新
          const needsRefresh = hasCookie && hasLoginElement;

          console.log('[页面检查] Cookie存在:', hasCookie, '登录界面显示:', hasLoginElement, '需要刷新:', needsRefresh);

          return needsRefresh;
        } catch (error) {
          console.error('[页面检查] 检查失败:', error);
          return false;
        }
      })()
    `);

    return needsRefresh;
  } catch (error) {
    console.error('检查页面是否需要刷新失败:', error);
    return false;
  }
}

// 强制特定网页的登录状态保持
async function forceLoginStateForSpecificWebsites(url, win) {
  try {
    console.log(`检查URL是否需要强制登录状态保持: ${url}`);

    const specificWebsites = [
      'store.weixin.qq.com',
      'filehelper.weixin.qq.com',
      'channels.weixin.qq.com'
    ];

    const isSpecificWebsite = specificWebsites.some(domain => url.includes(domain));

    if (isSpecificWebsite) {
      console.log(`检测到特定网页，执行强制登录状态保持: ${url}`);

      // 1. 强制恢复特定网页的Cookie
      await cookieManager.restoreSpecificWebsiteCookies();

      // 2. 延迟执行额外的状态检查和恢复
      setTimeout(async () => {
        try {
          // 检查页面是否仍需要登录
          const needsLogin = await win.webContents.executeJavaScript(`
            (function() {
              // 检查是否有登录相关的元素
              const loginElements = [
                document.querySelector('.login-container'),
                document.querySelector('.qr-code-login'),
                document.querySelector('.login-qr'),
                document.querySelector('.scan-login'),
                document.querySelector('.login-page'),
                document.querySelector('.qrcode-login')
              ];

              const hasLoginElement = loginElements.some(el => el !== null);

              // 检查是否有登录成功的元素
              const loggedInElements = [
                document.querySelector('.weui-desktop-account'),
                document.querySelector('.account-info'),
                document.querySelector('.file-list'),
                document.querySelector('.channels-container'),
                document.querySelector('.kf-container'),
                document.querySelector('.customer-service')
              ];

              const hasLoggedInElement = loggedInElements.some(el => el !== null);

              return hasLoginElement && !hasLoggedInElement;
            })()
          `);

          if (needsLogin) {
            console.log(`页面仍需要登录，尝试刷新页面: ${url}`);
            // 刷新页面以应用恢复的Cookie
            win.webContents.reload();
          } else {
            console.log(`页面登录状态正常: ${url}`);
            // 保存当前的登录状态
            await cookieManager.saveCompleteLoginState();
          }
        } catch (error) {
          console.error('检查页面登录状态失败:', error);
        }
      }, 3000);

      // 3. 设置定期检查，确保登录状态持续保持
      const intervalId = setInterval(async () => {
        try {
          if (win.isDestroyed()) {
            clearInterval(intervalId);
            return;
          }

          const currentUrl = win.webContents.getURL();
          if (specificWebsites.some(domain => currentUrl.includes(domain))) {
            // 定期保存登录状态
            await cookieManager.checkAndSaveLoginState();
          } else {
            clearInterval(intervalId);
          }
        } catch (error) {
          console.error('定期检查登录状态失败:', error);
        }
      }, 60000); // 每分钟检查一次
    }
  } catch (error) {
    console.error('强制登录状态保持失败:', error);
  }
}

// 处理来自preload-browser.js的IPC消息
async function handlePreloadIpcMessage(channel, data, win) {
  try {
    console.log(`[主进程] 处理preload-browser IPC消息: ${channel}`, data);

    switch (channel) {
      case 'login-state-changed':
        console.log('收到登录状态变化通知:', data);

        if (data) {
          // 登录成功，立即保存完整状态
          await cookieManager.saveCompleteLoginState();

          // 延迟再次保存，确保所有数据都已加载
          setTimeout(async () => {
            try {
              await cookieManager.saveCompleteLoginState();
              await saveSessionState();
              console.log('延迟保存登录状态完成');
            } catch (err) {
              console.error('延迟保存登录状态失败:', err);
            }
          }, 5000);
        }
        break;

      case 'force-save-login-state':
        console.log('收到强制保存登录状态请求');

        // 保存完整登录状态
        await cookieManager.saveCompleteLoginState();

        // 保存会话状态
        await saveSessionState();

        // 强制保存Cookie
        await cookieManager.forceSaveCookies();

        console.log('强制保存登录状态完成');
        break;

      case 'save-session-state':
        console.log('收到保存会话状态请求');
        await cookieManager.forceSaveCookies();
        break;

      case 'save-cookie':
        console.log('收到保存Cookie请求:', data);
        // 新的Cookie管理器会自动处理Cookie保存
        await cookieManager.forceSaveCookies();
        break;

      case 'restore-key-cookies':
        console.log('收到恢复关键Cookie请求');
        await cookieManager.restoreAllCookies();
        break;

      default:
        console.log(`未知的preload-browser IPC消息: ${channel}`);
        break;
    }
  } catch (error) {
    console.error(`处理preload-browser IPC消息失败 (${channel}):`, error);
  }
}

// 【新增】清除店铺检测状态
async function clearShopDetectionState() {
  try {
    console.log('🧹 清除店铺检测状态...');

    // 清除店铺检测相关的存储项
    const keysToRemove = [
      'xmh_shop_check_success',
      'xmh_shop_mismatch',
      'selected_shop_id',
      'selected_shop_name',
      'current_shop_info',
      'shop_selection_state',
      'shop_check_status',
      'shop_detection_completed',
      'xmh_recent_login_time' // 【新增】清除最近登录时间
    ];

    keysToRemove.forEach(key => {
      store.delete(key);
      console.log(`已清除店铺检测状态: ${key}`);
    });

    // 清除全局店铺检测状态
    global.shopDetectionCompleted = false;
    global.currentShopCheckStatus = null;

    console.log('✅ 店铺检测状态清除完成');
  } catch (error) {
    console.error('清除店铺检测状态失败:', error);
  }
}

// 【彻底重写】处理页面退出时的完整清理
async function handlePageExitCleanup(exitData) {
  try {
    console.log('🧹 开始执行彻底的页面退出清理流程...');

    // 1. 立即清除所有session分区
    console.log('🧹 清除所有Electron session分区...');
    try {
      // 获取所有session分区
      const allSessions = session.getAllSessions ? session.getAllSessions() : [];
      for (const sess of allSessions) {
        try {
          await sess.clearStorageData({
            storages: ['appcache', 'cookies', 'filesystem', 'indexdb', 'localstorage', 'shadercache', 'websql', 'serviceworkers', 'cachestorage']
          });
          await sess.clearCache();
          console.log('✅ 已清除一个session分区的所有数据');
        } catch (e) {
          console.log('清除session分区失败:', e);
        }
      }
    } catch (e) {
      console.log('获取所有session失败，使用默认清理方式:', e);
    }

    // 2. 清除Cookie管理器中的登录状态
    await cookieManager.clearLoginState();
    console.log('✅ 已清除Cookie管理器登录状态');

    // 3. 彻底清除所有持久化Cookie和Session数据
    await cookieManager.clearAllCookies();
    console.log('✅ 已彻底清除所有持久化Cookie和Session数据');

    // 4. 清除店铺检测状态
    await clearShopDetectionState();
    console.log('✅ 已清除店铺检测状态');

    // 5. 【增强】清除所有相关的存储项
    console.log('🧹 清除所有相关的存储项...');
    const allKeys = store.store;
    Object.keys(allKeys).forEach(key => {
      if (key.includes('shop') ||
          key.includes('store') ||
          key.includes('xmh_') ||
          key.includes('xiaomeihua') ||
          key.includes('check_status') ||
          key.includes('mismatch') ||
          key.includes('login') ||
          key.includes('session') ||
          key.includes('cookie') ||
          key.includes('auth') ||
          key.includes('token') ||
          key.includes('detection') ||
          key.includes('page_exit') ||
          key.includes('logout')) {
        store.delete(key);
        console.log('页面退出清理: 已清除存储项: ' + key);
      }
    });

    // 6. 【增强】清除所有全局状态
    console.log('🧹 清除所有全局状态...');
    global.shopInfoCache = null;
    global.currentShopId = null;
    global.selectedShopName = null;
    global.shopDetectionCompleted = false;
    global.currentShopCheckStatus = null;

    // 7. 【新增】清除所有店铺窗口
    console.log('🧹 关闭所有店铺窗口...');
    for (const [shopId, shopWindow] of Object.entries(shopWindows)) {
      try {
        if (shopWindow && !shopWindow.isDestroyed()) {
          shopWindow.close();
          console.log(`✅ 已关闭店铺窗口: ${shopId}`);
        }
      } catch (e) {
        console.log(`关闭店铺窗口失败: ${shopId}`, e);
      }
    }
    // 清空店铺窗口映射
    Object.keys(shopWindows).forEach(key => delete shopWindows[key]);

    // 8. 【新增】清除所有持久化文件
    console.log('🧹 清除所有持久化文件...');
    try {
      const userDataPath = app.getPath('userData');
      const filesToClear = [
        'persistent-cookies',
        'Session Storage',
        'Local Storage',
        'IndexedDB',
        'Cache',
        'Code Cache',
        'GPUCache',
        'logs'
      ];

      filesToClear.forEach(dirName => {
        const dirPath = path.join(userDataPath, dirName);
        if (fs.existsSync(dirPath)) {
          try {
            fs.rmSync(dirPath, { recursive: true, force: true });
            console.log(`✅ 已删除目录: ${dirName}`);
          } catch (e) {
            console.log(`删除目录失败: ${dirName}`, e);
          }
        }
      });

      // 清除单个文件
      const singleFiles = [
        'Cookies',
        'Cookies-journal',
        'Network Persistent State',
        'Preferences',
        'TransportSecurity'
      ];

      singleFiles.forEach(fileName => {
        const filePath = path.join(userDataPath, fileName);
        if (fs.existsSync(filePath)) {
          try {
            fs.unlinkSync(filePath);
            console.log(`✅ 已删除文件: ${fileName}`);
          } catch (e) {
            console.log(`删除文件失败: ${fileName}`, e);
          }
        }
      });

    } catch (e) {
      console.log('清除持久化文件失败:', e);
    }

    // 6. 设置页面退出标记，确保下次登录是全新状态
    store.set('page_exited', true);
    store.set('page_exit_timestamp', Date.now());
    store.set('page_exit_event', exitData.eventType);

    console.log('🎯 页面退出清理完成，下次登录将是全新状态');

  } catch (error) {
    console.error('页面退出清理失败:', error);
  }
}

// Windows平台专用数据获取函数
async function getSessionDataForWindows() {
  try {
    if (fs.existsSync(SESSION_DATA_PATH)) {
      return JSON.parse(fs.readFileSync(SESSION_DATA_PATH, 'utf-8'));
    }
  } catch (error) {
    console.warn('获取会话数据失败:', error.message);
  }
  return {};
}

async function getLocalStorageDataForWindows() {
  try {
    if (fs.existsSync(LOCAL_STORAGE_PATH)) {
      return JSON.parse(fs.readFileSync(LOCAL_STORAGE_PATH, 'utf-8'));
    }
  } catch (error) {
    console.warn('获取本地存储数据失败:', error.message);
  }
  return {};
}

// 【增强】检查保存的登录状态
async function checkSavedLoginState() {
  try {
    // 检查是否有页面退出标记
    const pageExited = store.get('page_exited');
    if (pageExited) {
      console.log('检测到页面退出标记，清除所有登录状态');
      store.delete('page_exited');
      store.delete('page_exit_timestamp');
      store.delete('page_exit_event');
      return false; // 强制重新登录
    }

    // 【移除】退出登录检测逻辑已移至Cookie管理器的initializeWeixinStoreOnStartup方法
    // 这里不再处理退出登录标记，避免过早清除

    const loginStateFile = path.join(app.getPath('userData'), 'persistent-cookies', 'login-state.json');

    if (fs.existsSync(loginStateFile)) {
      const loginState = JSON.parse(fs.readFileSync(loginStateFile, 'utf-8'));
      console.log('找到保存的登录状态文件:', loginState);

      // 检查登录状态是否在有效期内（24小时内）
      const now = Date.now();
      const stateAge = now - loginState.timestamp;
      const maxAge = 24 * 60 * 60 * 1000; // 24小时

      if (stateAge < maxAge && loginState.hasLogin) {
        console.log('登录状态有效，可以尝试自动恢复');

        // 【Windows平台优化】使用专用存储系统检查和恢复状态
        if (process.platform === 'win32' && windowsPersistentStorage) {
          try {
            // 使用专用存储系统恢复状态
            const restoredState = await windowsPersistentStorage.restoreLoginState();
            if (restoredState && restoredState.hasLogin) {
              console.log('✅ Windows专用存储系统恢复登录状态成功');

              // 恢复Cookie到浏览器会话
              if (restoredState.loginData && restoredState.loginData.cookies) {
                await cookieManager.restoreCookiesFromData(restoredState.loginData.cookies);
                console.log('✅ Windows平台Cookie数据恢复完成');
              }

              return true;
            } else {
              console.log('⚠️ 专用存储系统未找到有效登录状态，尝试备用方案...');

              // 尝试使用传统登录管理器恢复
              if (windowsLoginManager) {
                const backupState = await windowsLoginManager.restoreLoginState();
                if (backupState && backupState.hasLogin) {
                  console.log('✅ 从备用登录管理器恢复成功');
                  if (backupState.cookies && backupState.cookies.length > 0) {
                    await cookieManager.restoreCookiesFromData(backupState.cookies);
                  }
                  return true;
                }
              }

              return false;
            }
          } catch (winError) {
            console.error('❌ Windows专用存储恢复失败:', winError);

            // 多重回退策略
            try {
              // 回退1: 传统登录管理器
              if (windowsLoginManager) {
                const backupState = await windowsLoginManager.restoreLoginState();
                if (backupState && backupState.hasLogin) {
                  console.log('✅ 回退到传统登录管理器成功');
                  return true;
                }
              }

              // 回退2: 传统Cookie文件检查
              const cookiesFile = path.join(app.getPath('userData'), 'persistent-cookies', 'cookies.json');
              if (fs.existsSync(cookiesFile)) {
                const cookiesData = fs.readFileSync(cookiesFile, 'utf-8');
                const cookies = JSON.parse(cookiesData);
                if (cookies && cookies.length > 0) {
                  console.log('✅ 回退到传统Cookie文件检查成功');
                  return true;
                }
              }

              return false;
            } catch (fallbackError) {
              console.error('❌ 所有回退方案都失败:', fallbackError);
              return false;
            }
          }
        } else {
          return true;
        }
      } else {
        console.log('登录状态已过期或无效');
        return false;
      }
    }

    return false;
  } catch (error) {
    console.error('检查保存的登录状态失败:', error);
    return false;
  }
}

// 尝试自动登录
async function attemptAutoLogin() {
  try {
    console.log('开始尝试自动登录...');

    // 检查是否有保存的卡密
    const savedLicense = store.get('license');
    if (!savedLicense) {
      console.log('没有保存的卡密，无法自动登录');
      return false;
    }

    console.log('找到保存的卡密，尝试验证...');

    // 【优化】微信小店状态管理已移至Cookie管理器的initializeWeixinStoreOnStartup方法
    // 这里不再需要额外的状态检查，因为在应用启动时已经处理过了
    console.log('📋 微信小店状态已在启动时处理，继续验证卡密...');

    // 尝试验证卡密
    const result = await verifyLicense(savedLicense);

    if (result.success) {
      console.log('卡密验证成功，准备创建主窗口');

      // 延迟创建主窗口，确保Cookie已经恢复
      setTimeout(async () => {
        try {
          // 确保Cookie管理器已经恢复了所有登录状态
          await cookieManager.restoreLoginState();

          // 【关键修复】检查是否应该恢复微信小店登录状态
          try {
            console.log('🔄 检查是否应该恢复微信小店登录状态...');

            // 检查是否有退出登录标记
            const logoutDetected = store.get('logout_detected');
            const manualLogout = store.get('manual_logout');
            const pageExited = store.get('page_exited');

            if (logoutDetected || manualLogout || pageExited) {
              console.log('⚠️ 检测到退出登录标记，跳过登录状态恢复，确保可以重新扫码登录');

              // 【关键修复】确保完全清理登录状态，避免误判
              await cookieManager.resetWeixinStoreLoginState();
              await cookieManager.clearWeixinStoreSession();

              // 清理所有退出标记
              store.delete('logout_detected');
              store.delete('manual_logout');
              store.delete('page_exited');
              store.delete('page_exit_timestamp');
              store.delete('page_exit_event');

              console.log('✅ 退出登录状态清理完成，确保可以重新扫码登录');
            } else {
              console.log('🔄 恢复微信小店登录状态...');
              await cookieManager.restoreWeixinLoginState();
              console.log('✅ 微信小店登录状态恢复完成');
            }
          } catch (err) {
            console.error('❌ 微信小店登录状态恢复失败:', err);
          }

          // 强制恢复特定网页的Cookie
          await cookieManager.restoreSpecificWebsiteCookies();

          // 强制保存一次Cookie，确保状态同步
          await cookieManager.forceSaveCookies();

          if (loginWindow && !loginWindow.isDestroyed()) {
            loginWindow.close();
            loginWindow = null;
          }

          createMainWindow(result.shopInfo);
          console.log('自动登录成功，主窗口已创建，登录状态已恢复');

          // 创建窗口后再次确保Cookie状态和特定网页的强制保持
          setTimeout(async () => {
            try {
              await cookieManager.checkAndSaveLoginState();

              // 为所有打开的窗口检查是否需要强制登录状态保持
              const windows = BrowserWindow.getAllWindows();
              for (const win of windows) {
                if (!win.isDestroyed() && win.webContents) {
                  const url = win.webContents.getURL();
                  if (url) {
                    await forceLoginStateForSpecificWebsites(url, win);
                  }
                }
              }

              console.log('主窗口创建后，登录状态检查和强制保持完成');
            } catch (err) {
              console.error('主窗口创建后登录状态检查失败:', err);
            }
          }, 3000);

        } catch (error) {
          console.error('自动登录过程中恢复登录状态失败:', error);
          // 即使恢复失败，也创建主窗口
          if (loginWindow && !loginWindow.isDestroyed()) {
            loginWindow.close();
            loginWindow = null;
          }
          createMainWindow(result.shopInfo);
        }
      }, 2000); // 增加延迟时间，确保Cookie管理器完全初始化

      return true;
    } else {
      console.log('卡密验证失败，无法自动登录:', result.message);
      return false;
    }
  } catch (error) {
    console.error('自动登录过程中发生错误:', error);
    return false;
  }
}

// 注入脚本
function injectScript(webContents, retries = 5) {
  if (retries <= 0) {
    console.error('注入脚本失败: 达到最大重试次数');
    return;
  }

  try {
    console.log('开始注入脚本...');
    
    // 获取卡密验证脚本
    const { getKamiyanScript, getXinkamiScript, getTampermonkeyScript, getAIKnowledgeScript, createInjectionScript } = require('./kamiyan-injector');
    
    // 获取许可证密钥
    const licenseKey = store.get('license');
    if (!licenseKey) {
      console.error('无法注入脚本：缺少许可证密钥');
      return;
    }
    
    // 获取店铺信息
    const shopInfo = {
      shopName: shopName || '未知店铺',
      shopId: shopId || '',
      wechatStoreId: shopId || ''
    };
    
    console.log('准备注入脚本，店铺信息:', shopInfo);

    // 检查是否为AI知识库页面
    const currentUrl = webContents.getURL();
    const isAIKnowledgePage = currentUrl.includes('ai-knowledge.html') ||
                             currentUrl.includes('ai-knowledge') ||
                             currentUrl.includes('knowledge.php');

    let injectionScript;

    if (isAIKnowledgePage) {
      console.log('🤖 检测到AI知识库页面，注入增强脚本');
      // 为AI知识库页面创建专用脚本
      const aiKnowledgeScript = getAIKnowledgeScript();
      injectionScript = `
        // 设置全局卡密变量
        window.xiaomeihuaLicenseKey = "${licenseKey}";

        // 设置店铺信息
        window.xiaomeihuaShopInfo = ${JSON.stringify(shopInfo)};

        // 标记为APP环境
        window.isXiaomeihuaApp = true;

        // 注入AI知识库脚本
        ${aiKnowledgeScript}

        // 等待页面加载完成后增强功能
        document.addEventListener('DOMContentLoaded', function() {
          console.log('🚀 AI知识库页面DOM加载完成，开始增强功能');

          // 不再清空页面，而是增强现有功能
          // 通知页面脚本已注入
          if (window.aiKnowledgeAPI && typeof window.aiKnowledgeAPI.addKnowledgeItem === 'function') {
            setTimeout(() => {
              window.aiKnowledgeAPI.addKnowledgeItem(
                '🎯 APP增强功能已激活',
                '通过小梅花APP访问，Tampermonkey脚本已成功注入，所有高级功能现已可用。',
                '#007bff'
              );
            }, 2000);
          }

          console.log('✅ AI知识库页面增强完成');
        });

        // 立即执行检查
        if (document.readyState === 'complete') {
          console.log('🚀 页面已完全加载，立即增强功能');
          if (window.aiKnowledgeAPI && typeof window.aiKnowledgeAPI.addKnowledgeItem === 'function') {
            setTimeout(() => {
              window.aiKnowledgeAPI.addKnowledgeItem(
                '⚡ 即时增强已激活',
                'APP环境检测成功，所有增强功能立即可用。',
                '#28a745'
              );
            }, 1000);
          }
        }
      `;
    } else {
      // 使用注入脚本生成器创建完整的注入脚本
      injectionScript = createInjectionScript(licenseKey, shopInfo);
    }

    if (!injectionScript) {
      console.error('创建注入脚本失败');
      return;
    }
    
    // 修改：添加脚本加载地址修正
    const scriptFixCode = `
      // 修正脚本加载地址
      if (typeof window.API_CONFIG !== 'undefined') {
        console.log('修正脚本加载地址为verify.php');
        if (window.API_CONFIG.endpoints) {
          window.API_CONFIG.endpoints.script = '/api/verify.php';
          window.API_CONFIG.endpoints.verify = '/api/verify.php';
        }
      }
      
      // 修正所有xinkami.js的请求
      const originalFetch = window.fetch;
      window.fetch = function(url, options) {
        if (typeof url === 'string' && url.includes('/api/xinkami.js')) {
          console.log('拦截到fetch请求，修正为verify.php:', url);
          url = url.replace('/api/xinkami.js', '/api/verify.php');
        }
        return originalFetch(url, options);
      };
      
      // 修正XMLHttpRequest
      const originalOpen = XMLHttpRequest.prototype.open;
      XMLHttpRequest.prototype.open = function(method, url, ...args) {
        if (typeof url === 'string' && url.includes('/api/xinkami.js')) {
          console.log('拦截到XMLHttpRequest请求，修正为verify.php:', url);
          url = url.replace('/api/xinkami.js', '/api/verify.php');
        }
        return originalOpen.call(this, method, url, ...args);
      };
      
      // 添加自动验证函数
      window.autoVerifyXiaomeihua = function() {
        console.log('执行自动验证...');
        const licenseKey = "${licenseKey}";
        
        // 创建请求对象
        const xhr = new XMLHttpRequest();
        xhr.open('POST', 'https://xiaomeihuakefu.cn/api/verify.php', true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        
        // 设置回调
        xhr.onload = function() {
          if (xhr.status === 200) {
            try {
              const response = JSON.parse(xhr.responseText);
              console.log('自动验证响应:', response);
              
              if (response && response.success) {
                console.log('自动验证成功');
                
                // 执行初始化
                if (typeof window.xiaomeihuaForceInit === 'function') {
                  window.xiaomeihuaForceInit();
                }
              } else {
                console.error('自动验证失败:', response.message || '未知错误');
              }
            } catch (error) {
              console.error('解析自动验证响应失败:', error);
            }
          } else {
            console.error('自动验证请求失败:', xhr.status);
          }
        };
        
        xhr.onerror = function() {
          console.error('自动验证网络错误');
          
          // 网络错误时，尝试使用备用方法初始化
          if (typeof window.xiaomeihuaForceInit === 'function') {
            window.xiaomeihuaForceInit();
          }
        };
        
        // 发送请求
        xhr.send('key=' + encodeURIComponent(licenseKey));
        
        return '自动验证请求已发送';
      };
      
      // 设置定时执行自动验证
      setTimeout(window.autoVerifyXiaomeihua, 2000);
      setInterval(window.autoVerifyXiaomeihua, 30000);
    `;
    
    // 注入脚本
    webContents.executeJavaScript(injectionScript)
      .then(() => {
        console.log('脚本注入成功');
        
        // 注入脚本地址修正代码
        webContents.executeJavaScript(scriptFixCode)
          .then(() => {
            console.log('脚本地址修正代码注入成功');
          })
          .catch(err => {
            console.error('脚本地址修正代码注入失败:', err);
          });
        
        // 延迟后检查脚本是否正确加载
        setTimeout(() => {
          checkScriptLoaded();
          
          // 添加强制初始化代码
          webContents.executeJavaScript(`
            (function() {
              console.log('脚本注入后强制初始化检查...');
              
              // 检查UI元素是否存在
              const hasFloatingIcon = !!document.getElementById('floating-icon');
              const hasControlPanel = !!document.getElementById('control-panel');
              
              if (!hasFloatingIcon && !hasControlPanel) {
                console.log('UI元素不存在，尝试强制初始化...');
                
                // 尝试使用xiaomeihuaForceInit函数
                if (typeof window.xiaomeihuaForceInit === 'function') {
                  console.log('调用xiaomeihuaForceInit函数...');
                  window.xiaomeihuaForceInit();
                  return '正在强制初始化UI';
                }
                
                // 尝试使用main函数
                if (typeof window.main === 'function') {
                  console.log('调用main函数...');
                  window.main();
                  
                  // 延迟后检查是否需要创建UI元素
                  setTimeout(function() {
                    if (!document.getElementById('floating-icon')) {
                      console.log('main函数执行后仍未创建UI元素，尝试直接创建...');
                      
                      if (typeof window.createFloatingIcon === 'function') {
                        window.createFloatingIcon();
                      }
                      
                      if (typeof window.createControlPanel === 'function') {
                        window.createControlPanel();
                      }
                      
                      if (typeof window.restorePanelState === 'function') {
                        window.restorePanelState();
                      }
                    }
                  }, 2000);
                }
                
                return '尝试强制初始化';
              }
              
              return 'UI元素已存在，无需强制初始化';
            })();
          `)
          .then(result => {
            console.log('强制初始化结果:', result);
            
            // 添加强制显示浮动图标的代码
            setTimeout(() => {
              webContents.executeJavaScript(`
                (function() {
                  // 创建一个强制显示浮动图标的函数
                  function forceShowFloatingIcon() {
                    console.log('强制显示浮动图标...');
                    
                    // 检查浮动图标是否存在
                    let icon = document.getElementById('floating-icon');
                    
                    if (!icon) {
                      console.log('浮动图标不存在，尝试创建...');
                      
                      // 尝试创建浮动图标
                      if (typeof window.createFloatingIcon === 'function') {
                        icon = window.createFloatingIcon();
                      } else {
                        console.log('无法创建浮动图标：createFloatingIcon函数不存在');
                        return '无法创建浮动图标';
                      }
                    }
                    
                    // 强制设置样式确保可见
                    icon.style.cssText = \`
                      position: fixed !important;
                      top: 20px;
                      left: 20px;
                      width: 60px;
                      height: 60px;
                      border-radius: 50%;
                      cursor: pointer;
                      z-index: 2147483647 !important;
                      display: block !important;
                      opacity: 1 !important;
                      transform: scale(1);
                      background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500, #e55039, #3c6382);
                      background-size: 300% 300%;
                      visibility: visible !important;
                      pointer-events: auto !important;
                    \`;
                    
                    // 确保图标在DOM中
                    if (!document.body.contains(icon)) {
                      document.body.appendChild(icon);
                    }
                    
                    // 重新添加到body末尾确保在最上层
                    document.body.appendChild(icon);
                    
                    // 添加样式修复
                    const style = document.createElement('style');
                    style.textContent = \`
                      #floating-icon {
                        position: fixed !important;
                        z-index: 2147483647 !important;
                        display: block !important;
                        opacity: 1 !important;
                        visibility: visible !important;
                        pointer-events: auto !important;
                      }
                    \`;
                    document.head.appendChild(style);
                    
                    return '浮动图标已强制显示';
                  }
                  
                  // 立即执行
                  const result = forceShowFloatingIcon();
                  
                  // 设置定时器，每隔一段时间检查并强制显示
                  setInterval(forceShowFloatingIcon, 5000);
                  
                  return result;
                })();
              `)
              .then(iconResult => {
                console.log('强制显示浮动图标结果:', iconResult);
              })
              .catch(err => {
                console.error('强制显示浮动图标失败:', err);
              });
            }, 3000);
          })
          .catch(err => {
            console.error('强制初始化失败:', err);
          });
        }, 2000);
      })
      .catch(err => {
        console.error('脚本注入失败:', err);
        // 重试注入脚本
        setTimeout(() => {
          injectScript(webContents, retries - 1);
        }, 2000);
      });
  } catch (error) {
    console.error('注入脚本失败:', error);
    // 重试注入脚本
    setTimeout(() => {
      injectScript(webContents, retries - 1);
    }, 2000);
  }
}

// 【新增】显示关于窗口 - 修复登录窗口关于菜单bug
function showAboutWindow() {
  // 如果关于窗口已经打开，则聚焦到它
  if (aboutWindow && !aboutWindow.isDestroyed()) {
    aboutWindow.focus();
    return;
  }

  // 【修复】确定父窗口：如果主窗口存在则使用主窗口，否则使用登录窗口
  let parentWindow = null;
  if (mainWindow && !mainWindow.isDestroyed()) {
    parentWindow = mainWindow;
  } else if (loginWindow && !loginWindow.isDestroyed()) {
    parentWindow = loginWindow;
  }

  // 创建关于窗口 - 500x350纯白背景，无边框，可拖拽
  aboutWindow = new BrowserWindow({
    width: 500,
    height: 350,
    resizable: false,
    fullscreenable: false,
    maximizable: false,
    minimizable: false,
    alwaysOnTop: false,
    modal: false,
    parent: parentWindow,
    title: '关于小梅花AI智能客服',
    frame: false, // 无边框
    transparent: false, // 不透明，避免黑色背景
    backgroundColor: '#FFFFFF', // 纯白色背景
    hasShadow: true, // 启用阴影以便区分窗口边界
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'about-preload.js'),
      webSecurity: true
    },
    icon: path.join(__dirname, '../build/icon.png'),
    show: false
  });

  // 【修复】添加窗口位置居中逻辑
  if (parentWindow) {
    const [parentX, parentY] = parentWindow.getPosition();
    const [parentWidth, parentHeight] = parentWindow.getSize();
    const aboutX = parentX + Math.floor((parentWidth - 500) / 2);
    const aboutY = parentY + Math.floor((parentHeight - 350) / 2);
    aboutWindow.setPosition(aboutX, aboutY);
  } else {
    aboutWindow.center();
  }

  // 加载关于页面
  aboutWindow.loadFile(path.join(__dirname, 'renderer/about.html'));

  // 页面准备好后显示窗口
  aboutWindow.once('ready-to-show', () => {
    aboutWindow.show();
    
    // 发送版本信息
    const version = require('../package.json').version;
    aboutWindow.webContents.send('version-info', version);
    
    // 发送协议信息
    sendAgreementsToAboutWindow();
  });

  // 窗口关闭时清空引用
  aboutWindow.on('closed', () => {
    aboutWindow = null;
  });

  // 防止新窗口打开
  aboutWindow.webContents.setWindowOpenHandler(() => {
    return { action: 'deny' };
  });

  // 【新增】防止关于窗口触发任何登录相关逻辑
  aboutWindow.webContents.on('did-finish-load', () => {
    console.log('关于窗口加载完成，不会触发任何登录逻辑');
  });
}

// 【新增】向关于窗口发送协议信息
async function sendAgreementsToAboutWindow() {
  if (!aboutWindow || aboutWindow.isDestroyed()) {
    return;
  }

  try {
    // 使用APP设置API获取协议列表
    if (appSettingsAPI) {
      const result = await appSettingsAPI.getAgreements();
      if (result.success && result.agreements) {
        const publishedAgreements = result.agreements.filter(agreement => agreement.status === 'published');
        aboutWindow.webContents.send('agreements-info', publishedAgreements);
      } else {
        aboutWindow.webContents.send('agreements-info', []);
      }
    } else {
      // 如果API未初始化，发送空数组
      aboutWindow.webContents.send('agreements-info', []);
    }
  } catch (error) {
    console.error('获取协议信息失败:', error);
    aboutWindow.webContents.send('agreements-info', []);
  }
}

// 【新增】关于窗口的IPC处理
ipcMain.on('close-about-window', () => {
  if (aboutWindow && !aboutWindow.isDestroyed()) {
    aboutWindow.close();
  }
});

ipcMain.on('minimize-about-window', () => {
  if (aboutWindow && !aboutWindow.isDestroyed()) {
    aboutWindow.minimize();
  }
});

ipcMain.on('open-agreement', (event, agreement) => {
  // 打开协议弹窗，参考软件设置中的协议显示方式
  if (agreementManager) {
    agreementManager.showAgreementWindow(agreement);
  } else {
    console.error('协议管理器未初始化');
  }
});

ipcMain.on('request-version-info', (event) => {
  const version = require('../package.json').version;
  event.reply('version-info', version);
});

ipcMain.on('request-agreements-info', async (event) => {
  try {
    console.log('收到协议信息请求，appSettingsAPI状态:', !!appSettingsAPI);

    if (appSettingsAPI) {
      console.log('调用 getAgreements API...');
      const result = await appSettingsAPI.getAgreements();
      console.log('getAgreements 结果:', result);

      if (result.success && result.agreements && result.agreements.length > 0) {
        // 过滤已发布的协议，如果没有status字段则认为是已发布的
        const publishedAgreements = result.agreements.filter(agreement =>
          !agreement.status || agreement.status === 'published'
        );
        console.log('已发布的协议数量:', publishedAgreements.length);
        console.log('协议详情:', publishedAgreements.map(a => ({ title: a.title, status: a.status })));
        event.reply('agreements-info', publishedAgreements);
      } else {
        console.log('API调用失败或无协议数据，尝试备用方法...');

        // 尝试直接获取单个协议作为备用
        try {
          const singleResult = await appSettingsAPI.getAgreement('privacy');
          if (singleResult.success && singleResult.agreement) {
            console.log('备用方法获取到协议:', singleResult.agreement.title);
            event.reply('agreements-info', [singleResult.agreement]);
          } else {
            console.log('备用方法也失败');
            event.reply('agreements-info', []);
          }
        } catch (backupError) {
          console.error('备用方法失败:', backupError);
          event.reply('agreements-info', []);
        }
      }
    } else {
      console.log('appSettingsAPI 未初始化');
      event.reply('agreements-info', []);
    }
  } catch (error) {
    console.error('获取协议信息失败:', error);
    event.reply('agreements-info', []);
  }
});
