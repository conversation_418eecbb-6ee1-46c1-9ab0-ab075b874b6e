<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI知识库访问测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .success {
            background: #e8f5e8;
            border-color: #4caf50;
            color: #2e7d32;
        }
        .error {
            background: #ffebee;
            border-color: #f44336;
            color: #c62828;
        }
        .test-button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #1976d2;
        }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI知识库访问测试</h1>
        
        <div class="info-box">
            <h3>环境信息</h3>
            <div id="env-info">正在检测...</div>
        </div>
        
        <div class="info-box">
            <h3>访问检查结果</h3>
            <div id="access-result">等待测试...</div>
        </div>
        
        <div>
            <button class="test-button" onclick="testAccess()">测试AI知识库访问</button>
            <button class="test-button" onclick="testAPI()">测试API访问</button>
            <button class="test-button" onclick="openAIPage()">打开AI知识库页面</button>
        </div>
        
        <div class="info-box">
            <h3>测试日志</h3>
            <pre id="test-log"></pre>
        </div>
    </div>

    <script>
        let logContent = '';
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logContent += `[${timestamp}] ${message}\n`;
            document.getElementById('test-log').textContent = logContent;
        }
        
        function displayEnvInfo() {
            const envInfo = {
                userAgent: navigator.userAgent,
                protocol: location.protocol,
                hostname: location.hostname,
                href: location.href,
                isTop: window.self === window.top,
                hasProcess: typeof window.process !== 'undefined',
                hasElectron: typeof window.electron !== 'undefined',
                hasRequire: typeof window.require !== 'undefined'
            };
            
            document.getElementById('env-info').innerHTML = `
                <strong>User-Agent:</strong> ${envInfo.userAgent}<br>
                <strong>协议:</strong> ${envInfo.protocol}<br>
                <strong>主机:</strong> ${envInfo.hostname}<br>
                <strong>是否顶层窗口:</strong> ${envInfo.isTop}<br>
                <strong>Electron环境:</strong> ${envInfo.hasProcess || envInfo.hasElectron || envInfo.hasRequire}<br>
                <strong>完整URL:</strong> ${envInfo.href}
            `;
            
            log('环境信息已显示');
        }
        
        function checkAppEnvironment() {
            const userAgent = navigator.userAgent;
            
            // 检查User-Agent
            const isElectronApp = userAgent.includes('xiaomeihua-app') || 
                                 userAgent.includes('Electron') ||
                                 userAgent.includes('Chrome') && window.process;
            
            if (!isElectronApp) {
                log('❌ User-Agent检查失败: ' + userAgent);
                return false;
            }
            
            // 检查是否有Electron特有的API
            if (typeof window.require !== 'undefined' || 
                typeof window.process !== 'undefined' ||
                typeof window.electron !== 'undefined') {
                log('✅ 检测到Electron环境');
                return true;
            }
            
            // 检查是否在webview中
            const isInWebview = window.self !== window.top || 
                               window.location.protocol === 'file:' ||
                               window.location.hostname === 'xiaomeihuakefu.cn';
            
            if (isInWebview) {
                log('✅ 检测到webview环境');
                return true;
            }
            
            log('✅ 基于User-Agent允许访问');
            return true;
        }
        
        function checkSecurity() {
            const allowedProtocols = ['https:', 'http:', 'file:'];
            if (!allowedProtocols.includes(location.protocol)) {
                log('❌ 协议检查失败: ' + location.protocol);
                return false;
            }
            
            const allowedDomains = ['xiaomeihuakefu.cn', 'localhost', '127.0.0.1', ''];
            const isAllowedDomain = allowedDomains.some(domain => 
                location.hostname.includes(domain) || location.hostname === domain
            ) || location.protocol === 'file:';
            
            if (!isAllowedDomain) {
                log('❌ 域名检查失败: ' + location.hostname);
                return false;
            }
            
            log('✅ 安全检查通过');
            return true;
        }
        
        async function testAccess() {
            log('开始访问测试...');
            
            const appCheck = checkAppEnvironment();
            const securityCheck = checkSecurity();
            
            const result = appCheck && securityCheck;
            
            const resultDiv = document.getElementById('access-result');
            if (result) {
                resultDiv.innerHTML = '<div class="success">✅ 访问检查通过！AI知识库应该可以正常访问。</div>';
                log('✅ 所有检查通过');
            } else {
                resultDiv.innerHTML = '<div class="error">❌ 访问检查失败！请检查环境配置。</div>';
                log('❌ 访问检查失败');
            }
        }
        
        async function testAPI() {
            log('开始API测试...');
            
            try {
                const response = await fetch('./api/knowledge.php?action=script', {
                    method: 'GET',
                    headers: {
                        'X-App-Token': 'xiaomeihua-ai-knowledge-2025',
                        'User-Agent': navigator.userAgent
                    }
                });
                
                log(`API响应状态: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log('✅ API访问成功');
                    log('API响应: ' + JSON.stringify(data, null, 2));
                } else {
                    const errorText = await response.text();
                    log('❌ API访问失败: ' + errorText);
                }
            } catch (error) {
                log('❌ API请求错误: ' + error.message);
            }
        }
        
        function openAIPage() {
            log('打开AI知识库页面...');
            window.open('./ai-knowledge.html', '_blank');
        }
        
        // 页面加载时显示环境信息
        window.onload = function() {
            displayEnvInfo();
            log('测试页面已加载');
        };
    </script>
</body>
</html>
