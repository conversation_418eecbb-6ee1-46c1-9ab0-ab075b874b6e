-- 为scripts表添加URL匹配功能
-- 执行时间: 2025-08-10
-- 功能: 为脚本添加URL匹配规则，实现精准的页面脚本加载

-- 检查并添加url_patterns字段（兼容不同MySQL版本）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
       AND TABLE_NAME = 'scripts'
       AND COLUMN_NAME = 'url_patterns') > 0,
    'SELECT "字段url_patterns已存在，跳过添加" as message',
    'ALTER TABLE `scripts` ADD COLUMN `url_patterns` text DEFAULT NULL COMMENT "URL匹配规则，JSON格式存储多个URL"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 创建索引（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
       AND TABLE_NAME = 'scripts'
       AND INDEX_NAME = 'idx_url_patterns') > 0,
    'SELECT "索引idx_url_patterns已存在，跳过创建" as message',
    'CREATE INDEX `idx_url_patterns` ON `scripts` (`url_patterns`(255))'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 更新现有脚本的默认URL匹配规则（可选，如果需要为现有脚本设置默认规则）
-- UPDATE `scripts` SET `url_patterns` = '["*"]' WHERE `url_patterns` IS NULL;

-- 验证字段是否添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'scripts' 
    AND COLUMN_NAME = 'url_patterns';
