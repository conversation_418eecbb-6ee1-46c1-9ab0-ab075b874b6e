<?php
// 不要在这里设置header，因为index.php已经输出了内容
$message = '';

// 脚本处理函数 - 优化版：保留完整代码，不自动清理头部信息
function processUserScript($code) {
    $result = [
        'name' => '',
        'version' => '1.0.0',
        'code' => $code  // 保留完整的原始代码
    ];

    // 检查是否包含userscript头部
    if (strpos($code, '// ==UserScript==') !== false) {
        // 提取脚本名称
        if (preg_match('/@name\s+(.+)/i', $code, $matches)) {
            $result['name'] = trim($matches[1]);
        }

        // 提取版本号
        if (preg_match('/@version\s+(.+)/i', $code, $matches)) {
            $result['version'] = trim($matches[1]);
        }

        // 【优化】不再自动清理头部信息，保留完整代码
        // 原来的代码会自动截取从 (function() { 开始的部分
        // 现在改为保留完整的脚本代码，包括 UserScript 头部信息
        $result['code'] = $code;  // 保持原始代码不变
    }

    return $result;
}

// 处理AJAX状态切换请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'toggle_script_status') {
    header('Content-Type: application/json');
    
    // 确保session已启动
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    // 检查用户是否已登录
    if (!isset($_SESSION['admin_user_id'])) {
        echo json_encode(['success' => false, 'message' => '用户未登录']);
        exit;
    }
    
    $script_id = intval($_POST['script_id'] ?? 0);
    $new_status = $_POST['new_status'] ?? '';
    
    if ($script_id > 0 && in_array($new_status, ['active', 'inactive'])) {
        try {
            // 确保scripts表有status字段
            $pdo->exec("ALTER TABLE scripts ADD COLUMN IF NOT EXISTS status ENUM('active','inactive') DEFAULT 'active'");
            
            $stmt = $pdo->prepare("UPDATE scripts SET status = ? WHERE id = ?");
            if ($stmt->execute([$new_status, $script_id])) {
                echo json_encode(['success' => true, 'message' => '状态更新成功']);
            } else {
                $error_info = $stmt->errorInfo();
                echo json_encode(['success' => false, 'message' => '数据库更新失败: ' . $error_info[2]]);
            }
        } catch (Exception $e) {
            // 如果是字段不存在的错误，尝试添加字段
            if (strpos($e->getMessage(), 'status') !== false || strpos($e->getMessage(), 'Unknown column') !== false) {
                try {
                    $pdo->exec("ALTER TABLE scripts ADD COLUMN status ENUM('active','inactive') DEFAULT 'active'");
                    $stmt = $pdo->prepare("UPDATE scripts SET status = ? WHERE id = ?");
                    if ($stmt->execute([$new_status, $script_id])) {
                        echo json_encode(['success' => true, 'message' => '状态更新成功']);
                    } else {
                        $error_info = $stmt->errorInfo();
                        echo json_encode(['success' => false, 'message' => '数据库更新失败: ' . $error_info[2]]);
                    }
                } catch (Exception $e2) {
                    echo json_encode(['success' => false, 'message' => '更新失败：' . $e2->getMessage()]);
                }
            } else {
                echo json_encode(['success' => false, 'message' => '更新失败：' . $e->getMessage()]);
            }
        }
    } else {
        echo json_encode(['success' => false, 'message' => '参数错误：script_id=' . $script_id . ', new_status=' . $new_status]);
    }
    exit;
}

// 处理片段脚本清理请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'clear_snippet_scripts') {
        try {
            // 开始事务
            $pdo->beginTransaction();
            
            // 检查表是否存在
            $stmt = $pdo->query("SHOW TABLES LIKE 'snippet_scripts'");
            if ($stmt->rowCount() > 0) {
                // 获取清理前的数量
                $count_stmt = $pdo->query("SELECT COUNT(*) FROM snippet_scripts");
                $before_count = $count_stmt->fetchColumn();
                
                // 显示要删除的数据（用于调试）
                $debug_stmt = $pdo->query("SELECT id, title, category FROM snippet_scripts");
                $snippets = $debug_stmt->fetchAll(PDO::FETCH_ASSOC);
                
                // 强制清空片段脚本表
                $delete_stmt = $pdo->prepare("DELETE FROM snippet_scripts");
                $delete_stmt->execute();
                $actual_deleted = $delete_stmt->rowCount();
                
                // 重置自增ID
                $pdo->exec("ALTER TABLE snippet_scripts AUTO_INCREMENT = 1");
                
                // 同时清理关联表（如果存在）
                $script_snippets_exists = $pdo->query("SHOW TABLES LIKE 'script_snippets'")->rowCount() > 0;
                if ($script_snippets_exists) {
                    $pdo->exec("DELETE FROM script_snippets");
                    $pdo->exec("ALTER TABLE script_snippets AUTO_INCREMENT = 1");
                }
                
                // 验证清理结果
                $after_count = $pdo->query("SELECT COUNT(*) FROM snippet_scripts")->fetchColumn();
                
                // 提交事务
                $pdo->commit();
                
                $message = "<div class='message success'><i class='fas fa-check-circle'></i> 强制清理完成！清理前: {$before_count} 个，实际删除: {$actual_deleted} 个，清理后: {$after_count} 个</div>";
                echo $message;
                
                // 输出调试信息
                if (!empty($snippets)) {
                    echo "<div class='message success'><i class='fas fa-info-circle'></i> 已删除的片段脚本: ";
                    foreach ($snippets as $snippet) {
                        echo "{$snippet['title']} ({$snippet['category']}) ";
                    }
                    echo "</div>";
                }
                
            } else {
                echo "<div class='message error'><i class='fas fa-exclamation-triangle'></i> snippet_scripts表不存在</div>";
                $pdo->rollBack();
            }
        } catch (Exception $e) {
            $pdo->rollBack();
            echo "<div class='message error'><i class='fas fa-times-circle'></i> 清理失败: " . $e->getMessage() . "</div>";
        }
        exit();
    }
    
    if ($_POST['action'] === 'delete_snippet') {
        $snippet_id = intval($_POST['snippet_id'] ?? 0);
        if ($snippet_id > 0) {
            try {
                $stmt = $pdo->prepare("DELETE FROM snippet_scripts WHERE id = ?");
                if ($stmt->execute([$snippet_id])) {
                    echo "<div class='message success'><i class='fas fa-check-circle'></i> 片段脚本已删除</div>";
                } else {
                    echo "<div class='message error'><i class='fas fa-times-circle'></i> 删除失败</div>";
                }
            } catch (Exception $e) {
                echo "<div class='message error'><i class='fas fa-times-circle'></i> 删除失败: " . $e->getMessage() . "</div>";
            }
        }
        exit();
    }
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['save_snippet'])) {
        $snippet_id = $_POST['snippet_id'] ?? null;
        $title = trim($_POST['title']);
        $description = trim($_POST['description'] ?? '');
        $code = $_POST['code'];
        $category = $_POST['category'] ?? 'default';
        $sort_order = (int)($_POST['sort_order'] ?? 0);
        $load_priority = (int)($_POST['load_priority'] ?? 50);

        if (empty($title) || empty($code)) {
            $message = "<div class='message error'><i class='fas fa-exclamation-triangle'></i> 片段名称和代码都不能为空</div>";
        } else {
            // 确保snippet_scripts表存在
            $pdo->exec("CREATE TABLE IF NOT EXISTS `snippet_scripts` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `title` varchar(255) NOT NULL,
                `description` text,
                `code` longtext NOT NULL,
                `enabled` tinyint(1) DEFAULT 1,
                `sort_order` int(11) DEFAULT 0,
                `category` varchar(100) DEFAULT 'default',
                `load_priority` int(11) DEFAULT 50,
                `dependencies` text,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `idx_enabled` (`enabled`),
                KEY `idx_sort_order` (`sort_order`),
                KEY `idx_category` (`category`),
                KEY `idx_load_priority` (`load_priority`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

            if ($snippet_id) {
                $stmt = $pdo->prepare("UPDATE snippet_scripts SET title = ?, description = ?, code = ?, category = ?, sort_order = ?, load_priority = ? WHERE id = ?");
                if ($stmt->execute([$title, $description, $code, $category, $sort_order, $load_priority, $snippet_id])) {
                    $message = "<div class='message success'><i class='fas fa-check-circle'></i> 片段已成功更新！</div>";
                }
            } else {
                $stmt = $pdo->prepare("INSERT INTO snippet_scripts (title, description, code, category, sort_order, load_priority) VALUES (?, ?, ?, ?, ?, ?)");
                if ($stmt->execute([$title, $description, $code, $category, $sort_order, $load_priority])) {
                    $message = "<div class='message success'><i class='fas fa-check-circle'></i> 片段已成功添加！</div>";
                }
            }
        }
    }
    
    if (isset($_POST['save_script'])) {
        $script_id = $_POST['script_id'] ?? null;
        $name = trim($_POST['name']);
        $version = trim($_POST['version']);
        $description = trim($_POST['description'] ?? '');
        $code = $_POST['script_code'];
        $url_patterns = $_POST['url_patterns'] ?? [];

        // 如果名称为空，尝试自动处理脚本
        if (empty($name) && !empty($code)) {
            $processed = processUserScript($code);
            $name = $processed['name'];
            $version = $processed['version'];
            $code = $processed['code'];
        }

        // 处理URL匹配规则
        $url_patterns_json = null;
        if (!empty($url_patterns) && is_array($url_patterns)) {
            // 过滤空值并验证URL格式
            $valid_patterns = [];
            foreach ($url_patterns as $pattern) {
                $pattern = trim($pattern);
                if (!empty($pattern)) {
                    // 基本URL格式验证
                    if (filter_var($pattern, FILTER_VALIDATE_URL) ||
                        preg_match('/^(\*|https?):\/\//', $pattern) ||
                        strpos($pattern, '://') !== false) {
                        $valid_patterns[] = $pattern;
                    }
                }
            }

            if (!empty($valid_patterns)) {
                $url_patterns_json = json_encode($valid_patterns, JSON_UNESCAPED_UNICODE);
            }
        }

        // 验证必填字段
        if (empty($name) || empty($version) || empty($code)) {
            $message = "<div class='message error'><i class='fas fa-exclamation-triangle'></i> 脚本名称、版本和代码都不能为空</div>";
        } elseif (empty($url_patterns_json)) {
            $message = "<div class='message error'><i class='fas fa-exclamation-triangle'></i> 请至少输入一个有效的URL匹配规则</div>";
        } else {
            if ($script_id) { // 更新
                $stmt = $pdo->prepare("UPDATE scripts SET name = ?, version = ?, description = ?, script_code = ?, url_patterns = ? WHERE id = ?");
                if ($stmt->execute([$name, $version, $description, $code, $url_patterns_json, $script_id])) {
                    $message = "<div class='message success'><i class='fas fa-check-circle'></i> 脚本已成功更新！</div>";
                } else {
                    $message = "<div class='message error'><i class='fas fa-times-circle'></i> 更新脚本失败</div>";
                }
            } else { // 新增
                $stmt = $pdo->prepare("INSERT INTO scripts (name, version, description, script_code, url_patterns) VALUES (?, ?, ?, ?, ?)");
                if ($stmt->execute([$name, $version, $description, $code, $url_patterns_json])) {
                    $message = "<div class='message success'><i class='fas fa-check-circle'></i> 脚本已成功添加！</div>";
                } else {
                    $message = "<div class='message error'><i class='fas fa-times-circle'></i> 添加脚本失败</div>";
                }
            }
        }
    }

    // 处理AI设置表单提交
    if (isset($_POST['save_ai_settings'])) {
        $script_id = $_POST['script_id'];
        $ai_enabled = isset($_POST['ai_enabled']) ? 1 : 0;
        $api_key = trim($_POST['api_key']);
        $model = $_POST['model'];
        $deep_thinking_enabled = isset($_POST['deep_thinking_enabled']) ? 1 : 0;
        $scan_interval = (int)$_POST['scan_interval'];
        $reply_delay = (int)$_POST['reply_delay'];

        if (empty($script_id)) {
            $message = "<div class='message error'><i class='fas fa-exclamation-triangle'></i> 请选择要配置的脚本</div>";
        } else {
            // 确保ai_settings表存在
            $pdo->exec("CREATE TABLE IF NOT EXISTS `ai_settings` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `script_id` int(11) NOT NULL,
                `ai_enabled` tinyint(1) DEFAULT 0,
                `api_key` varchar(255) DEFAULT NULL,
                `model` varchar(100) DEFAULT 'deepseek-chat',
                `deep_thinking_enabled` tinyint(1) DEFAULT 0,
                `scan_interval` int(11) DEFAULT 5,
                `reply_delay` int(11) DEFAULT 0,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `unique_script` (`script_id`),
                KEY `idx_ai_enabled` (`ai_enabled`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

            // 检查是否已存在配置
            $stmt = $pdo->prepare("SELECT id FROM ai_settings WHERE script_id = ?");
            $stmt->execute([$script_id]);
            $existing = $stmt->fetch();

            if ($existing) {
                // 更新现有配置
                $stmt = $pdo->prepare("UPDATE ai_settings SET ai_enabled = ?, api_key = ?, model = ?, deep_thinking_enabled = ?, scan_interval = ?, reply_delay = ?, updated_at = NOW() WHERE script_id = ?");
                if ($stmt->execute([$ai_enabled, $api_key, $model, $deep_thinking_enabled, $scan_interval, $reply_delay, $script_id])) {
                    $message = "<div class='message success'><i class='fas fa-check-circle'></i> AI设置已成功更新！</div>";
                } else {
                    $message = "<div class='message error'><i class='fas fa-times-circle'></i> 更新AI设置失败</div>";
                }
            } else {
                // 创建新配置
                $stmt = $pdo->prepare("INSERT INTO ai_settings (script_id, ai_enabled, api_key, model, deep_thinking_enabled, scan_interval, reply_delay) VALUES (?, ?, ?, ?, ?, ?, ?)");
                if ($stmt->execute([$script_id, $ai_enabled, $api_key, $model, $deep_thinking_enabled, $scan_interval, $reply_delay])) {
                    $message = "<div class='message success'><i class='fas fa-check-circle'></i> AI设置已成功保存！</div>";
                } else {
                    $message = "<div class='message error'><i class='fas fa-times-circle'></i> 保存AI设置失败</div>";
                }
            }
        }
    }
}

// 删除脚本
if (isset($_GET['delete_script'])) {
    $script_id = $_GET['delete_script'];
    
    // 检查是否有卡密关联此脚本
    $stmt_check = $pdo->prepare("SELECT COUNT(*) FROM license_keys WHERE script_id = ?");
    $stmt_check->execute([$script_id]);
    $linked_keys = $stmt_check->fetchColumn();
    
    if ($linked_keys > 0) {
        $message = "<div class='message error'><i class='fas fa-exclamation-triangle'></i> 无法删除：还有 {$linked_keys} 个卡密关联此脚本</div>";
    } else {
        $stmt = $pdo->prepare("DELETE FROM scripts WHERE id = ?");
        if ($stmt->execute([$script_id])) {
            $message = "<div class='message success'><i class='fas fa-check-circle'></i> 脚本已删除！</div>";
        }
    }
}

// 获取要编辑的脚本
$edit_script = null;
if (isset($_GET['edit_script'])) {
    $stmt = $pdo->prepare("SELECT * FROM scripts WHERE id = ?");
    $stmt->execute([$_GET['edit_script']]);
    $edit_script = $stmt->fetch(PDO::FETCH_ASSOC);
}

// 获取脚本列表 - 包含状态字段
$scripts_sql = "
    SELECT s.id,
           s.name,
           s.version,
           s.description,
           s.script_code,
           s.status,
           s.created_at,
           (SELECT COUNT(*) FROM license_keys WHERE script_id = s.id) as linked_keys_count
    FROM scripts s 
    ORDER BY s.id DESC
";
$scripts = $pdo->query($scripts_sql)->fetchAll(PDO::FETCH_ASSOC);

// 确保每个脚本都有默认值
foreach ($scripts as &$script) {
    $script['name'] = $script['name'] ?? '';
    $script['version'] = $script['version'] ?? '1.0.0';
    $script['description'] = $script['description'] ?? '';
    $script['status'] = $script['status'] ?? 'active'; // 默认启用
    $script['linked_keys_count'] = isset($script['linked_keys_count']) ? (int)$script['linked_keys_count'] : 0;
    $script['created_at'] = $script['created_at'] ?? date('Y-m-d H:i:s');
}
?>

<style>
    /* 脚本管理页面专用样式 */
    .scripts-container {
        max-width: 100%;
        margin: 0;
        padding: 20px;
        box-sizing: border-box;
    }

    /* 模块导航样式 */
    .module-navigation {
        display: flex;
        justify-content: center;
        margin-bottom: 30px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 15px;
        padding: 8px;
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .nav-tab {
        flex: 1;
        max-width: 250px;
        padding: 15px 25px;
        background: transparent;
        border: none;
        color: rgba(255, 255, 255, 0.7);
        font-size: 16px;
        font-weight: 500;
        border-radius: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    .nav-tab:hover {
        color: white;
        background: rgba(255, 255, 255, 0.1);
        transform: translateY(-2px);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
    }

    .nav-tab.active {
        background: linear-gradient(135deg, #ff6b9d, #c44569);
        color: white;
        box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
    }

    .nav-tab i {
        font-size: 18px;
    }

    /* 模块内容样式 */
    .module-content {
        display: none;
        animation: fadeIn 0.3s ease-in;
    }

    .module-content.active {
        display: block;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .card {
        background: rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(20px) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        border-radius: 15px !important;
        padding: 20px !important;
        margin-bottom: 20px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
    }
    
    .card h2 {
        color: white !important;
        margin-bottom: 20px !important;
        font-size: 18px !important;
        font-weight: 600 !important;
        display: flex !important;
        align-items: center !important;
        gap: 10px !important;
    }
    
    .form-inline {
        display: flex !important;
        flex-direction: column !important;
        gap: 0 !important;
        margin-bottom: 20px !important;
    }
    
    @media (max-width: 768px) {
        .form-inline {
            flex-direction: column !important;
        }
    }
    
    .form-group {
        margin-bottom: 20px !important;
    }
    
    .form-group label {
        display: block !important;
        color: white !important;
        margin-bottom: 8px !important;
        font-weight: 500 !important;
        font-size: 14px !important;
    }
    
    .form-group input,
    .form-group textarea,
    .form-group select {
        width: 100% !important;
        padding: 12px 15px !important;
        background: rgba(255, 255, 255, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        border-radius: 8px !important;
        color: white !important;
        font-size: 14px !important;
        transition: all 0.3s ease !important;
    }
    
    .form-group input::placeholder,
    .form-group textarea::placeholder {
        color: rgba(255, 255, 255, 0.5) !important;
    }
    
    .form-group input:focus,
    .form-group textarea:focus,
    .form-group select:focus {
        outline: none !important;
        border-color: #ff6b9d !important;
        box-shadow: 0 0 0 2px rgba(255, 107, 157, 0.2) !important;
    }
    
    .table-container {
        overflow-x: auto !important;
        border-radius: 8px !important;
        background: rgba(255, 255, 255, 0.05) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
    }
    
    table {
        width: 100% !important;
        border-collapse: collapse !important;
        font-size: 14px !important;
    }
    
    table th {
        background: rgba(255, 255, 255, 0.1) !important;
        color: white !important;
        padding: 15px 12px !important;
        text-align: left !important;
        font-weight: 600 !important;
        font-size: 14px !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
        white-space: nowrap !important;
    }
    
    table td {
        padding: 15px 12px !important;
        color: rgba(255, 255, 255, 0.9) !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
        vertical-align: middle !important;
        font-size: 13px !important;
    }
    
    table tr:hover {
        background: rgba(255, 255, 255, 0.05) !important;
    }
    
    .status-badge {
        display: inline-block !important;
        padding: 6px 12px !important;
        border-radius: 12px !important;
        font-size: 12px !important;
        font-weight: 500 !important;
        background: rgba(255, 255, 255, 0.2) !important;
        color: white !important;
    }
    
    .status-active {
        background: rgba(255, 255, 255, 0.2) !important;
        color: white !important;
        border: 1px solid rgba(255, 255, 255, 0.5) !important;
    }
    
    .status-disabled {
        background: rgba(220, 53, 69, 0.3) !important;
        color: #dc3545 !important;
    }
    
    .btn {
        display: inline-flex !important;
        align-items: center !important;
        gap: 8px !important;
        padding: 10px 20px !important;
        border: none !important;
        border-radius: 8px !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        text-decoration: none !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #ff6b9d, #c44569) !important;
        color: white !important;
    }
    
    .btn-secondary {
        background: rgba(255, 255, 255, 0.2) !important;
        color: white !important;
    }
    
    .btn-success {
        background: linear-gradient(135deg, #4ade80, #22c55e) !important;
        color: white !important;
    }
    
    .btn-warning {
        background: linear-gradient(135deg, #fbbf24, #f59e0b) !important;
        color: white !important;
    }
    
    .btn-danger {
        background: linear-gradient(135deg, #f87171, #ef4444) !important;
        color: white !important;
    }
    
    .btn:hover {
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
    }
    
    .message {
        padding: 15px 20px !important;
        border-radius: 8px !important;
        margin-bottom: 20px !important;
        display: flex !important;
        align-items: center !important;
        gap: 10px !important;
        font-size: 14px !important;
        font-weight: 500 !important;
    }
    
    .message.success {
        background: rgba(34, 197, 94, 0.2) !important;
        border: 1px solid rgba(34, 197, 94, 0.3) !important;
        color: #4ade80 !important;
    }
    
    .message.error {
        background: rgba(239, 68, 68, 0.2) !important;
        border: 1px solid rgba(239, 68, 68, 0.3) !important;
        color: #f87171 !important;
    }
    
    .script-description {
        font-size: 12px !important;
        opacity: 0.7 !important;
        margin-top: 3px !important;
        line-height: 1.3 !important;
    }
    
    .script-name {
        font-weight: 600 !important;
        color: white !important;
        margin-bottom: 3px !important;
        font-size: 14px !important;
    }
    
    .action-buttons {
        display: flex !important;
        gap: 8px !important;
        justify-content: center !important;
        flex-wrap: wrap !important;
    }
    
    .action-buttons .btn {
        padding: 8px 12px !important;
        font-size: 12px !important;
        min-width: 36px !important;
    }
    
    .auto-process-info {
        background: rgba(52, 152, 219, 0.2) !important;
        border: 1px solid rgba(52, 152, 219, 0.3) !important;
        color: white !important;
        padding: 15px 20px !important;
        border-radius: 8px !important;
        margin-bottom: 20px !important;
        font-size: 14px !important;
    }

    /* AI设置专用样式 */
    .ai-toggle-group {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 20px;
        padding: 15px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    .ai-switch {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 24px;
    }

    .ai-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .ai-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.2);
        transition: .4s;
        border-radius: 24px;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    .ai-slider:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    input:checked + .ai-slider {
        background: rgba(74, 222, 128, 0.4);
        border: 1px solid rgba(74, 222, 128, 0.6);
    }

    input:checked + .ai-slider:before {
        transform: translateX(26px);
    }

    .ai-toggle-label {
        color: white;
        font-weight: 500;
        font-size: 16px;
    }

    .ai-status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-left: 10px;
    }

    .ai-status-enabled {
        background-color: #4ade80;
        box-shadow: 0 0 8px rgba(74, 222, 128, 0.5);
    }

    .ai-status-disabled {
        background-color: #f87171;
    }

    .api-key-input {
        position: relative;
    }

    .api-key-toggle {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: rgba(255, 255, 255, 0.6);
        cursor: pointer;
        font-size: 16px;
        transition: color 0.3s ease;
    }

    .api-key-toggle:hover {
        color: white;
    }
</style>

<div class="scripts-container">
    <!-- 模块导航 -->
    <div class="module-navigation">
        <button class="nav-tab active" data-module="scripts">
            <i class="fas fa-file-code"></i> 脚本列表管理
        </button>
        <button class="nav-tab" data-module="snippets">
            <i class="fas fa-puzzle-piece"></i> 片段脚本管理
        </button>
        <button class="nav-tab" data-module="deepseek">
            <i class="fas fa-brain"></i> DeepSeek API
        </button>
    </div>

    <!-- 脚本列表管理模块 -->
    <div id="scripts-module" class="module-content active">
        <div class="card">
            <h2><i class="fas fa-plus-circle"></i> <?php echo $edit_script ? '编辑脚本' : '添加脚本'; ?></h2>
        
        <div class="auto-process-info">
            <i class="fas fa-info-circle"></i>
            智能处理：粘贴完整的UserScript代码，系统将自动提取脚本名称、版本号，<strong>保留完整的头部信息</strong>
        </div>
        
        <?php echo $message; ?>
        
        <form method="POST" id="scriptForm">
            <?php if ($edit_script): ?>
                <input type="hidden" name="script_id" value="<?php echo $edit_script['id']; ?>">
            <?php endif; ?>
            
            <div class="form-inline">
                <div class="form-group">
                    <label><i class="fas fa-tag"></i> 脚本名称</label>
                    <input type="text" name="name" id="scriptName" value="<?php echo htmlspecialchars($edit_script['name'] ?? '', ENT_QUOTES, 'UTF-8'); ?>" placeholder="例如：小梅花AI智能客服">
                </div>
                <div class="form-group">
                    <label><i class="fas fa-code-branch"></i> 版本号</label>
                    <input type="text" name="version" id="scriptVersion" value="<?php echo htmlspecialchars($edit_script['version'] ?? '1.0.0', ENT_QUOTES, 'UTF-8'); ?>" placeholder="例如：1.0.0">
                </div>
            </div>
            
            <div class="form-group">
                <label><i class="fas fa-info-circle"></i> 脚本描述 (可选)</label>
                <input type="text" name="description" value="<?php echo htmlspecialchars($edit_script['description'] ?? '', ENT_QUOTES, 'UTF-8'); ?>" placeholder="简单描述脚本功能">
            </div>
            
            <div class="form-group">
                <label><i class="fas fa-code"></i> 脚本代码</label>
                <textarea name="script_code" id="scriptCode" rows="10" required placeholder="在此粘贴您的JavaScript代码或完整的UserScript..."><?php echo htmlspecialchars($edit_script['script_code'] ?? '', ENT_QUOTES, 'UTF-8'); ?></textarea>
            </div>
            
            <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-top: 10px;">
                <button type="submit" name="save_script" class="btn btn-primary">
                    <i class="fas fa-save"></i> <?php echo $edit_script ? '更新脚本' : '保存脚本'; ?>
                </button>
                
                <button type="button" onclick="processScript()" class="btn btn-success">
                    <i class="fas fa-magic"></i> 提取信息
                </button>
                
                <?php if ($edit_script): ?>
                    <a href="index.php?page=scripts" class="btn btn-secondary">
                        <i class="fas fa-times"></i> 取消编辑
                    </a>
                <?php endif; ?>
            </div>
        </form>
    </div>

    <div class="card">
        <h2><i class="fas fa-list"></i> 脚本管理</h2>
        
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; flex-wrap: wrap; gap: 15px;">
            <div style="color: rgba(255,255,255,0.8); font-size: 14px;">
                <i class="fas fa-info-circle"></i> 
                脚本列表 (共 <?php echo count($scripts); ?> 个脚本)
            </div>
            
            <div style="display: flex; gap: 10px;">
                <a href="#snippet-scripts" class="btn btn-secondary">
                    <i class="fas fa-puzzle-piece"></i> 查看片段脚本
                </a>
                <button onclick="clearSnippetScripts()" class="btn btn-warning">
                    <i class="fas fa-broom"></i> 清理片段脚本
                </button>
            </div>
        </div>
        
        <?php if (empty($scripts)): ?>
            <p style="color: rgba(255,255,255,0.7); text-align: center; padding: 20px;">
                <i class="fas fa-info-circle"></i> 暂无脚本数据，请先添加脚本
            </p>
        <?php else: ?>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th style="width: 50px; text-align: center;"><i class="fas fa-hashtag"></i> ID</th>
                            <th><i class="fas fa-tag"></i> 脚本信息</th>
                            <th style="width: 100px; text-align: center;"><i class="fas fa-code-branch"></i> 版本</th>
                            <th style="width: 80px; text-align: center;"><i class="fas fa-link"></i> 关联</th>
                            <th style="width: 100px; text-align: center;"><i class="fas fa-toggle-on"></i> 状态</th>
                            <th style="width: 120px;"><i class="fas fa-calendar"></i> 创建时间</th>
                            <th style="width: 150px; text-align: center;"><i class="fas fa-cogs"></i> 操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($scripts as $script): ?>
                        <tr>
                            <td style="text-align: center; font-weight: bold;"><?php echo $script['id']; ?></td>
                            <td>
                                <div class="script-name"><?php echo htmlspecialchars($script['name'], ENT_QUOTES, 'UTF-8'); ?></div>
                                <?php if (!empty($script['description'])): ?>
                                    <div class="script-description"><?php echo htmlspecialchars($script['description'], ENT_QUOTES, 'UTF-8'); ?></div>
                                <?php endif; ?>
                            </td>
                            <td style="text-align: center;">
                                <span class="status-badge"><?php echo htmlspecialchars($script['version'], ENT_QUOTES, 'UTF-8'); ?></span>
                            </td>
                            <td style="text-align: center;">
                                <?php if ($script['linked_keys_count'] > 0): ?>
                                    <span class="status-badge status-active"><?php echo $script['linked_keys_count']; ?></span>
                                <?php else: ?>
                                    <span style="opacity: 0.5;">0</span>
                                <?php endif; ?>
                            </td>
                            <td style="text-align: center;">
                                <span class="status-badge <?php echo $script['status'] === 'active' ? 'status-active' : 'status-disabled'; ?>" 
                                      style="cursor: pointer;" 
                                      onclick="toggleStatus(<?php echo $script['id']; ?>)"
                                      title="点击切换状态">
                                    <?php echo $script['status'] === 'active' ? '启用' : '禁用'; ?>
                                </span>
            </td>
                            <td>
                                <?php echo date('Y-m-d H:i', strtotime($script['created_at'])); ?>
                            </td>
                            <td style="text-align: center;">
                                <div class="action-buttons">
                                    <a href="index.php?page=scripts&edit_script=<?php echo $script['id']; ?>" 
                                       class="btn btn-secondary" 
                                       title="编辑">
                                        <i class="fas fa-edit"></i> 编辑
                                    </a>
                                    
                                    <a href="index.php?page=scripts&delete_script=<?php echo $script['id']; ?>" 
                                       class="btn btn-danger" 
                                       title="删除"
                                       onclick="return confirm('确定要删除这个脚本吗？<?php echo $script['linked_keys_count'] > 0 ? '\\n注意：此脚本关联了 ' . $script['linked_keys_count'] . ' 个卡密！' : ''; ?>')">
                                        <i class="fas fa-trash"></i> 删除
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
    </div>

    <!-- 片段脚本管理模块 -->
    <div id="snippets-module" class="module-content">
        <div class="card">
            <h2><i class="fas fa-plus-circle"></i> 添加片段脚本</h2>
            
            <form method="POST" id="snippetForm">
                <div class="form-inline">
                    <div class="form-group">
                        <label><i class="fas fa-tag"></i> 片段名称</label>
                        <input type="text" name="title" id="snippetTitle" placeholder="例如：登录助手" required>
                    </div>
                    <div class="form-group">
                        <label><i class="fas fa-folder"></i> 分类</label>
                        <select name="category" id="snippetCategory">
                            <option value="default">默认</option>
                            <option value="helper">助手</option>
                            <option value="ui">界面</option>
                            <option value="api">接口</option>
                            <option value="util">工具</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label><i class="fas fa-info-circle"></i> 描述 (可选)</label>
                    <input type="text" name="description" placeholder="简单描述片段功能">
                </div>
                
                <div class="form-inline">
                    <div class="form-group">
                        <label><i class="fas fa-sort-numeric-up"></i> 排序</label>
                        <input type="number" name="sort_order" value="0" min="0" max="999">
                    </div>
                    <div class="form-group">
                        <label><i class="fas fa-layer-group"></i> 优先级</label>
                        <input type="number" name="load_priority" value="50" min="1" max="100">
                    </div>
                </div>
                
                <div class="form-group">
                    <label><i class="fas fa-code"></i> 片段代码</label>
                    <textarea name="code" rows="8" required placeholder="在此输入JavaScript代码片段..."></textarea>
                </div>
                
                <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-top: 10px;">
                    <button type="submit" name="save_snippet" class="btn btn-primary">
                        <i class="fas fa-save"></i> 保存片段
                    </button>
                    <button type="button" onclick="clearSnippetForm()" class="btn btn-secondary">
                        <i class="fas fa-eraser"></i> 清空表单
                    </button>
                </div>
            </form>
        </div>
        
        <div class="card">
            <h2><i class="fas fa-puzzle-piece"></i> 片段脚本列表</h2>
        
        <?php
        // 获取片段脚本数据
        try {
            $snippet_scripts = [];
            $snippet_count = 0;
            
            // 检查snippet_scripts表是否存在
            $tables = [];
            $stmt = $pdo->query("SHOW TABLES LIKE 'snippet_scripts'");
            if ($stmt->rowCount() > 0) {
                // 获取片段脚本列表
                $snippet_stmt = $pdo->query("SELECT * FROM snippet_scripts ORDER BY id DESC");
                $snippet_scripts = $snippet_stmt->fetchAll(PDO::FETCH_ASSOC);
                $snippet_count = count($snippet_scripts);
            }
        } catch (Exception $e) {
            $snippet_scripts = [];
            $snippet_count = 0;
        }
        ?>
        
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; flex-wrap: wrap; gap: 15px;">
            <div style="color: rgba(255,255,255,0.8); font-size: 14px;">
                <i class="fas fa-info-circle"></i> 
                片段脚本列表 (共 <?php echo $snippet_count; ?> 个片段)
            </div>
            
            <div style="display: flex; gap: 10px;">
                <?php if ($snippet_count > 0): ?>
                    <button onclick="clearSnippetScripts()" class="btn btn-danger">
                        <i class="fas fa-trash"></i> 清空所有片段
                    </button>
                <?php endif; ?>
                <a href="../智能数据清理脚本.php" target="_blank" class="btn btn-warning">
                    <i class="fas fa-broom"></i> 完整清理
                </a>
            </div>
        </div>
        
        <?php if (empty($snippet_scripts)): ?>
            <p style="color: rgba(255,255,255,0.7); text-align: center; padding: 40px;">
                <i class="fas fa-info-circle"></i> 暂无片段脚本数据
                <br><small style="margin-top: 10px; display: block;">片段脚本通常由代码编程功能或其他模块自动生成</small>
            </p>
        <?php else: ?>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th style="width: 50px; text-align: center;"><i class="fas fa-hashtag"></i> ID</th>
                            <th><i class="fas fa-tag"></i> 片段信息</th>
                            <th style="width: 100px; text-align: center;"><i class="fas fa-folder"></i> 分类</th>
                            <th style="width: 80px; text-align: center;"><i class="fas fa-toggle-on"></i> 状态</th>
                            <th style="width: 120px;"><i class="fas fa-calendar"></i> 创建时间</th>
                            <th style="width: 100px; text-align: center;"><i class="fas fa-cogs"></i> 操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($snippet_scripts as $snippet): ?>
                        <tr>
                            <td style="text-align: center; font-weight: bold;"><?php echo $snippet['id']; ?></td>
                            <td>
                                <div class="script-name"><?php echo htmlspecialchars($snippet['title'] ?? 'Untitled', ENT_QUOTES, 'UTF-8'); ?></div>
                                <?php if (!empty($snippet['description'])): ?>
                                    <div class="script-description"><?php echo htmlspecialchars($snippet['description'], ENT_QUOTES, 'UTF-8'); ?></div>
                                <?php endif; ?>
                                <div class="script-description" style="margin-top: 5px;">
                                    代码长度: <?php echo strlen($snippet['code'] ?? ''); ?> 字符
                                </div>
                            </td>
                            <td style="text-align: center;">
                                <span class="status-badge">
                                    <?php echo htmlspecialchars($snippet['category'] ?? 'default', ENT_QUOTES, 'UTF-8'); ?>
                                </span>
                            </td>
                            <td style="text-align: center;">
                                <?php $enabled = isset($snippet['enabled']) ? $snippet['enabled'] : 1; ?>
                                <span class="status-badge <?php echo $enabled ? 'status-active' : 'status-disabled'; ?>">
                                    <?php echo $enabled ? '启用' : '禁用'; ?>
                                </span>
                            </td>
                            <td>
                                <?php echo date('Y-m-d H:i', strtotime($snippet['created_at'] ?? 'now')); ?>
                            </td>
                            <td style="text-align: center;">
                                <button onclick="deleteSnippet(<?php echo $snippet['id']; ?>)" 
                                        class="btn btn-danger" 
                                        title="删除此片段"
                                        style="padding: 6px 10px; font-size: 11px;">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
        </div>
    </div>

    <!-- DeepSeek API模块 -->
    <div id="deepseek-module" class="module-content">
        <div class="card">
            <h2><i class="fas fa-brain"></i> DeepSeek AI智能回复设置</h2>
            
            <div class="auto-process-info">
                <i class="fas fa-info-circle"></i> 
                配置DeepSeek AI智能回复功能，当用户消息不匹配任何关键词时，系统将使用AI生成回复
            </div>
            
            <form method="POST" id="aiSettingsForm">
                <div class="form-group">
                    <label><i class="fas fa-code"></i> 选择脚本</label>
                    <select name="script_id" required>
                        <option value="">请选择要配置AI的脚本</option>
                        <?php foreach ($scripts as $script): ?>
                            <option value="<?php echo $script['id']; ?>">
                                <?php echo htmlspecialchars($script['name'], ENT_QUOTES, 'UTF-8'); ?> 
                                (v<?php echo htmlspecialchars($script['version'], ENT_QUOTES, 'UTF-8'); ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="ai-toggle-group">
                    <label class="ai-switch">
                        <input type="checkbox" name="ai_enabled">
                        <span class="ai-slider"></span>
                    </label>
                    <span class="ai-toggle-label">启用AI回复</span>
                    <span class="ai-status-indicator ai-status-disabled"></span>
                </div>
                
                <div class="form-inline">
                    <div class="form-group">
                        <label><i class="fas fa-key"></i> API密钥</label>
                        <div class="api-key-input">
                            <input type="password" name="api_key" id="apiKeyInput" placeholder="输入DeepSeek API密钥">
                            <button type="button" class="api-key-toggle" onclick="toggleApiKeyVisibility()">
                                <i class="fas fa-eye" id="apiKeyToggleIcon"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label><i class="fas fa-robot"></i> AI模型</label>
                        <select name="model">
                            <option value="deepseek-chat">DeepSeek Chat</option>
                            <option value="deepseek-reasoner">DeepSeek-R1-0528</option>
                        </select>
                    </div>
                </div>
                
                <div class="ai-toggle-group">
                    <label class="ai-switch">
                        <input type="checkbox" name="deep_thinking_enabled">
                        <span class="ai-slider"></span>
                    </label>
                    <span class="ai-toggle-label">深度思考 (R1)</span>
                    <small style="color: rgba(255,255,255,0.6); margin-left: 10px;">仅对DeepSeek-R1-0528模型有效</small>
                </div>
                
                <div class="form-inline">
                    <div class="form-group">
                        <label><i class="fas fa-clock"></i> 扫描间隔 (秒)</label>
                        <input type="number" name="scan_interval" min="1" max="60" value="5" placeholder="默认5秒">
                    </div>
                    
                    <div class="form-group">
                        <label><i class="fas fa-hourglass-half"></i> 回复延迟 (秒)</label>
                        <input type="number" name="reply_delay" min="0" max="60" value="0" placeholder="0为立即回复">
                    </div>
                </div>
                
                <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-top: 20px;">
                    <button type="submit" name="save_ai_settings" class="btn btn-primary">
                        <i class="fas fa-save"></i> 保存AI设置
                    </button>
                    <button type="button" onclick="clearAiForm()" class="btn btn-secondary">
                        <i class="fas fa-eraser"></i> 清空表单
                    </button>
                </div>
            </form>
        </div>

        <div class="card">
            <h2><i class="fas fa-list"></i> AI设置管理</h2>
            
            <?php
            // 获取AI设置数据
            try {
                $ai_settings_sql = "
                    SELECT 
                        ai.id,
                        ai.script_id,
                        ai.ai_enabled,
                        ai.api_key,
                        ai.model,
                        ai.deep_thinking_enabled,
                        ai.scan_interval,
                        ai.reply_delay,
                        ai.created_at,
                        ai.updated_at,
                        s.name as script_name,
                        s.version as script_version
                    FROM ai_settings ai
                    LEFT JOIN scripts s ON ai.script_id = s.id
                    ORDER BY ai.updated_at DESC
                ";
                $ai_settings = $pdo->query($ai_settings_sql)->fetchAll(PDO::FETCH_ASSOC);
            } catch (Exception $e) {
                $ai_settings = [];
            }
            ?>
            
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; flex-wrap: wrap; gap: 15px;">
                <div style="color: rgba(255,255,255,0.8); font-size: 14px;">
                    <i class="fas fa-info-circle"></i> 
                    AI设置列表 (共 <?php echo count($ai_settings); ?> 个配置)
                </div>
            </div>
            
            <?php if (empty($ai_settings)): ?>
                <p style="color: rgba(255,255,255,0.7); text-align: center; padding: 40px;">
                    <i class="fas fa-info-circle"></i> 暂无AI设置数据
                    <br><small style="margin-top: 10px; display: block;">请先选择脚本并配置AI设置</small>
                </p>
            <?php else: ?>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th style="width: 50px; text-align: center;"><i class="fas fa-hashtag"></i> ID</th>
                                <th><i class="fas fa-code"></i> 脚本信息</th>
                                <th style="width: 100px; text-align: center;"><i class="fas fa-robot"></i> 模型</th>
                                <th style="width: 80px; text-align: center;"><i class="fas fa-toggle-on"></i> 状态</th>
                                <th style="width: 80px; text-align: center;"><i class="fas fa-brain"></i> R1</th>
                                <th style="width: 120px;"><i class="fas fa-calendar"></i> 更新时间</th>
                                <th style="width: 100px; text-align: center;"><i class="fas fa-cogs"></i> 操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($ai_settings as $setting): ?>
                            <tr>
                                <td style="text-align: center; font-weight: bold;"><?php echo $setting['id']; ?></td>
                                <td>
                                    <div class="script-name"><?php echo htmlspecialchars($setting['script_name'] ?? 'Unknown', ENT_QUOTES, 'UTF-8'); ?></div>
                                    <div class="script-description">v<?php echo htmlspecialchars($setting['script_version'] ?? '1.0.0', ENT_QUOTES, 'UTF-8'); ?></div>
                                    <div class="script-description">
                                        扫描: <?php echo $setting['scan_interval']; ?>s | 延迟: <?php echo $setting['reply_delay']; ?>s
                                    </div>
                                </td>
                                <td style="text-align: center;">
                                    <span class="status-badge">
                                        <?php echo $setting['model'] === 'deepseek-reasoner' ? 'R1' : 'Chat'; ?>
                                    </span>
                                </td>
                                <td style="text-align: center;">
                                    <span class="status-badge <?php echo $setting['ai_enabled'] ? 'status-active' : 'status-disabled'; ?>">
                                        <?php echo $setting['ai_enabled'] ? '启用' : '禁用'; ?>
                                    </span>
                                </td>
                                <td style="text-align: center;">
                                    <span class="status-badge <?php echo $setting['deep_thinking_enabled'] ? 'status-active' : 'status-disabled'; ?>">
                                        <?php echo $setting['deep_thinking_enabled'] ? '开启' : '关闭'; ?>
                                    </span>
                                </td>
                                <td>
                                    <?php echo date('Y-m-d H:i', strtotime($setting['updated_at'])); ?>
                                </td>
                                <td style="text-align: center;">
                                    <button onclick="editAiSetting(<?php echo $setting['id']; ?>)" 
                                            class="btn btn-secondary" 
                                            title="编辑AI设置"
                                            style="padding: 6px 10px; font-size: 11px;">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// 模块切换功能
function switchModule(moduleName) {
    // 隐藏所有模块
    document.querySelectorAll('.module-content').forEach(module => {
        module.classList.remove('active');
    });
    
    // 移除所有导航标签的active状态
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // 激活选中的模块
    const targetModule = document.getElementById(moduleName + '-module');
    if (targetModule) {
        targetModule.classList.add('active');
    }
    
    // 激活对应的导航标签
    const targetTab = document.querySelector(`.nav-tab[data-module="${moduleName}"]`);
    if (targetTab) {
        targetTab.classList.add('active');
    }
    
    // 保存当前选中的模块到localStorage
    localStorage.setItem('xiaomeihua_active_module', moduleName);
}

// 绑定导航点击事件
document.addEventListener('DOMContentLoaded', function() {
    // 初始化模块导航
    initModuleNavigation();
    
    // 其他初始化代码...
    
    // AI启用状态切换监听
    const aiToggle = document.querySelector('input[name="ai_enabled"]');
    if (aiToggle) {
        aiToggle.addEventListener('change', updateAiStatusIndicator);
    }
});

// 模块导航初始化
function initModuleNavigation() {
    const navTabs = document.querySelectorAll('.nav-tab');
    
    // 从localStorage获取上次选中的模块
    const lastActiveModule = localStorage.getItem('xiaomeihua_active_module');
    
    // 默认激活第一个或上次选中的模块
    let activeTabFound = false;
    
    navTabs.forEach(tab => {
        const moduleName = tab.getAttribute('data-module');
        
        // 如果是上次选中的模块，则激活它
        if (lastActiveModule && moduleName === lastActiveModule) {
            switchModule(moduleName);
            tab.classList.add('active');
            activeTabFound = true;
        }
        
        // 添加点击事件
        tab.addEventListener('click', function() {
            const moduleName = this.getAttribute('data-module');
            switchModule(moduleName);
            
            // 保存当前选中的模块到localStorage
            localStorage.setItem('xiaomeihua_active_module', moduleName);
        });
    });
    
    // 如果没有找到上次选中的模块，则不需要做任何事情
    // 因为HTML默认已经激活了第一个模块
}

// 清空片段表单
function clearSnippetForm() {
    document.getElementById('snippetForm').reset();
    showToast('表单已清空', 'info');
}

// 片段状态切换
function toggleSnippetStatus(snippetId) {
    const statusElement = event.target;
    const currentStatus = statusElement.textContent.trim();
    const enabled = currentStatus === '启用' ? 0 : 1;
    
    statusElement.style.opacity = '0.6';
    statusElement.style.pointerEvents = 'none';
    
    const formData = new FormData();
    formData.append('action', 'toggle_snippet_status');
    formData.append('snippet_id', snippetId);
    formData.append('enabled', enabled);
    
    fetch('index.php?page=scripts', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            statusElement.textContent = enabled ? '启用' : '禁用';
            statusElement.className = 'status-badge ' + (enabled ? 'status-active' : 'status-disabled');
            showToast('片段状态已更新', 'success');
        } else {
            showToast('状态更新失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('状态更新失败，请重试', 'error');
    })
    .finally(() => {
        statusElement.style.opacity = '1';
        statusElement.style.pointerEvents = 'auto';
    });
}

function processScript() {
    const codeTextarea = document.getElementById('scriptCode');
    const nameInput = document.getElementById('scriptName');
    const versionInput = document.getElementById('scriptVersion');

    let code = codeTextarea.value;

    if (!code.trim()) {
        alert('请先输入脚本代码');
        return;
    }

    // 提取脚本名称
    const nameMatch = code.match(/@name\s+(.+)/i);
    if (nameMatch && !nameInput.value.trim()) {
        nameInput.value = nameMatch[1].trim();
    }

    // 提取版本号
    const versionMatch = code.match(/@version\s+(.+)/i);
    if (versionMatch) {
        versionInput.value = versionMatch[1].trim();
    }

    // 【优化】不再自动清理头部信息，保留完整代码
    // 原来的代码会自动截取从 (function() { 开始的部分
    // 现在改为只提取信息，不修改代码内容

    // 检查是否包含 UserScript 头部信息
    const hasUserScriptHeader = code.includes('// ==UserScript==');

    // 显示处理结果提示
    const successMsg = document.createElement('div');
    successMsg.className = 'message success';

    if (hasUserScriptHeader) {
        successMsg.innerHTML = '<i class="fas fa-check-circle"></i> 脚本信息提取完成！已保留完整的 UserScript 头部信息';
    } else {
        successMsg.innerHTML = '<i class="fas fa-info-circle"></i> 脚本信息提取完成！未检测到 UserScript 头部，代码保持原样';
    }

    successMsg.style.marginTop = '10px';

    const form = document.getElementById('scriptForm');
    form.appendChild(successMsg);

    // 3秒后移除提示
    setTimeout(() => {
        if (successMsg.parentNode) {
            successMsg.parentNode.removeChild(successMsg);
        }
    }, 3000);
}
}

// 自动处理粘贴的内容
document.getElementById('scriptCode').addEventListener('paste', function(e) {
    setTimeout(() => {
        const code = this.value;
        if (code.includes('// ==UserScript==')) {
            // 延迟执行自动处理
            setTimeout(processScript, 100);
        }
    }, 50);
});

// 启用/禁用切换功能
function toggleStatus(scriptId) {
    const statusElement = event.target;
    const currentStatus = statusElement.textContent.trim();
    const newStatus = currentStatus === '启用' ? 'inactive' : 'active';
    
    // 禁用按钮防止重复点击
    statusElement.style.opacity = '0.6';
    statusElement.style.pointerEvents = 'none';
    
    // 发送AJAX请求到后端更新数据库
    const formData = new FormData();
    formData.append('action', 'toggle_script_status');
    formData.append('script_id', scriptId);
    formData.append('new_status', newStatus);
    
    fetch('../api/ajax_handler.php', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // 更新UI
            statusElement.textContent = newStatus === 'active' ? '启用' : '禁用';
            statusElement.className = 'status-badge ' + (newStatus === 'active' ? 'status-active' : 'status-disabled');
            showToast('脚本状态已更新', 'success');
        } else {
            showToast('状态更新失败: ' + (data.message || '未知错误'), 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('状态更新失败，请重试', 'error');
    })
    .finally(() => {
        // 恢复按钮状态
        statusElement.style.opacity = '1';
        statusElement.style.pointerEvents = 'auto';
    });
}

// 简单的提示函数
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    
    let backgroundColor, borderColor;
    switch(type) {
        case 'success':
            backgroundColor = 'rgba(34, 197, 94, 0.2)';
            borderColor = '#22c55e';
            break;
        case 'error':
            backgroundColor = 'rgba(239, 68, 68, 0.2)';
            borderColor = '#ef4444';
            break;
        case 'warning':
            backgroundColor = 'rgba(245, 158, 11, 0.2)';
            borderColor = '#f59e0b';
            break;
        default:
            backgroundColor = 'rgba(0, 0, 0, 0.8)';
            borderColor = 'rgba(255, 255, 255, 0.2)';
    }
    
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${backgroundColor};
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        border: 1px solid ${borderColor};
        z-index: 10000;
        font-size: 14px;
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        animation: slideInRight 0.3s ease;
    `;
    toast.textContent = message;
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 300);
    }, 3000);
}

// 添加动画CSS
if (!document.getElementById('toast-animations')) {
    const style = document.createElement('style');
    style.id = 'toast-animations';
    style.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
}

// 清理片段脚本功能
function clearSnippetScripts() {
    if (!confirm('确定要清空所有片段脚本吗？\n\n此操作将删除所有片段脚本数据，不可恢复！')) {
        return;
    }
    
    showToast('正在清理片段脚本...', 'info');
    
    // 发送清理请求
    const formData = new FormData();
    formData.append('action', 'clear_snippet_scripts');
    
    fetch('index.php?page=scripts', {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(data => {
        if (data.includes('success') || data.includes('成功')) {
            showToast('片段脚本清理完成！', 'success');
            // 刷新页面显示结果
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showToast('清理失败，请手动使用完整清理功能', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('清理失败，请手动使用完整清理功能', 'error');
    });
}

// 删除单个片段脚本
function deleteSnippet(snippetId) {
    if (!confirm('确定要删除这个片段脚本吗？')) {
        return;
    }
    
    showToast('正在删除片段脚本...', 'info');
    
    const formData = new FormData();
    formData.append('action', 'delete_snippet');
    formData.append('snippet_id', snippetId);
    
    fetch('index.php?page=scripts', {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(data => {
        if (data.includes('success') || data.includes('成功')) {
            showToast('片段脚本已删除！', 'success');
            // 刷新页面显示结果
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showToast('删除失败，请重试', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('删除失败，请重试', 'error');
    });
}

// AI密钥显示/隐藏切换
function toggleApiKeyVisibility() {
    const input = document.getElementById('apiKeyInput');
    const icon = document.getElementById('apiKeyToggleIcon');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        input.type = 'password';
        icon.className = 'fas fa-eye';
    }
}

// AI启用状态切换时更新状态指示器
function updateAiStatusIndicator() {
    const aiToggle = document.querySelector('input[name="ai_enabled"]');
    const statusIndicator = document.querySelector('.ai-status-indicator');
    
    if (aiToggle && statusIndicator) {
        if (aiToggle.checked) {
            statusIndicator.classList.remove('ai-status-disabled');
            statusIndicator.classList.add('ai-status-enabled');
        } else {
            statusIndicator.classList.remove('ai-status-enabled');
            statusIndicator.classList.add('ai-status-disabled');
        }
    }
}

// 清空AI表单
function clearAiForm() {
    document.getElementById('aiSettingsForm').reset();
    updateAiStatusIndicator();
    showToast('AI表单已清空', 'info');
}

// 编辑AI设置
function editAiSetting(settingId) {
    showToast('编辑功能开发中...', 'info');
}
</script> 