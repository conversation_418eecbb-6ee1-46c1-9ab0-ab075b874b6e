<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL匹配功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        input, button {
            padding: 8px 12px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>URL匹配功能测试</h1>
    
    <div class="test-container">
        <h2>手动测试</h2>
        <div>
            <label>URL: </label>
            <input type="text" id="testUrl" value="https://store.weixin.qq.com/shop/home" style="width: 300px;">
        </div>
        <div>
            <label>匹配规则: </label>
            <input type="text" id="testPattern" value="*://store.weixin.qq.com/*" style="width: 300px;">
        </div>
        <button onclick="testMatch()">测试匹配</button>
        <div id="manualResult"></div>
    </div>
    
    <div class="test-container">
        <h2>自动测试用例</h2>
        <button onclick="runAllTests()">运行所有测试</button>
        <div id="autoResults"></div>
    </div>

    <script>
        // URL匹配函数 - 与APP中的实现保持一致
        function matchUrl(url, pattern) {
            if (!pattern || !url) return false;
            
            // 如果模式是 * 则匹配所有URL
            if (pattern === '*') return true;
            
            // 转义正则表达式特殊字符，但保留 * 作为通配符
            const escapedPattern = pattern
                .replace(/[.+?^${}()|[\]\\]/g, '\\$&')  // 转义特殊字符
                .replace(/\\\*/g, '.*');  // 将 * 转换为 .*
            
            // 创建正则表达式进行匹配
            const regex = new RegExp('^' + escapedPattern + '$', 'i');
            const matched = regex.test(url);
            
            console.log('URL匹配检查:', {
                url: url,
                pattern: pattern,
                regex: regex.source,
                matched: matched
            });
            
            return matched;
        }

        function testMatch() {
            const url = document.getElementById('testUrl').value;
            const pattern = document.getElementById('testPattern').value;
            const result = matchUrl(url, pattern);
            
            const resultDiv = document.getElementById('manualResult');
            resultDiv.innerHTML = `
                <div class="test-result ${result ? 'success' : 'error'}">
                    <strong>结果:</strong> ${result ? '匹配成功' : '匹配失败'}<br>
                    <strong>URL:</strong> ${url}<br>
                    <strong>规则:</strong> ${pattern}
                </div>
            `;
        }

        function runAllTests() {
            const testCases = [
                // 基本匹配测试
                { url: 'https://store.weixin.qq.com/shop/home', pattern: '*://store.weixin.qq.com/*', expected: true },
                { url: 'https://store.weixin.qq.com/shop/home', pattern: 'https://store.weixin.qq.com/*', expected: true },
                { url: 'https://store.weixin.qq.com/shop/home', pattern: 'https://store.weixin.qq.com/shop/*', expected: true },
                { url: 'https://store.weixin.qq.com/shop/home', pattern: 'https://store.weixin.qq.com/shop/home', expected: true },
                
                // 不匹配测试
                { url: 'https://store.weixin.qq.com/shop/home', pattern: 'https://example.com/*', expected: false },
                { url: 'https://store.weixin.qq.com/shop/home', pattern: 'https://store.weixin.qq.com/admin/*', expected: false },
                
                // 通配符测试
                { url: 'https://any-site.com/any-path', pattern: '*', expected: true },
                { url: 'https://any-site.com/any-path', pattern: '*://any-site.com/*', expected: true },
                { url: 'https://any-site.com/any-path', pattern: 'https://*.com/*', expected: true },
                
                // 协议测试
                { url: 'http://store.weixin.qq.com/shop/home', pattern: '*://store.weixin.qq.com/*', expected: true },
                { url: 'https://store.weixin.qq.com/shop/home', pattern: 'http://store.weixin.qq.com/*', expected: false },
                
                // 边界情况测试
                { url: '', pattern: '*', expected: false },
                { url: 'https://example.com', pattern: '', expected: false },
                { url: 'https://example.com', pattern: null, expected: false }
            ];

            let results = '<h3>测试结果:</h3>';
            let passCount = 0;
            let totalCount = testCases.length;

            testCases.forEach((testCase, index) => {
                const result = matchUrl(testCase.url, testCase.pattern);
                const passed = result === testCase.expected;
                if (passed) passCount++;

                results += `
                    <div class="test-result ${passed ? 'success' : 'error'}">
                        <strong>测试 ${index + 1}:</strong> ${passed ? '通过' : '失败'}<br>
                        <strong>URL:</strong> ${testCase.url}<br>
                        <strong>规则:</strong> ${testCase.pattern}<br>
                        <strong>期望:</strong> ${testCase.expected ? '匹配' : '不匹配'}<br>
                        <strong>实际:</strong> ${result ? '匹配' : '不匹配'}
                    </div>
                `;
            });

            results += `
                <div class="test-result ${passCount === totalCount ? 'success' : 'error'}">
                    <strong>总结:</strong> ${passCount}/${totalCount} 个测试通过
                </div>
            `;

            document.getElementById('autoResults').innerHTML = results;
        }
    </script>
</body>
</html>
