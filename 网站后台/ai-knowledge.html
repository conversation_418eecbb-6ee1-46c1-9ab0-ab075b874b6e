<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI知识库</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #ffffff;
            min-height: 100vh;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        }

        /* 确保页面完全空白 */
        .container {
            width: 100%;
            height: 100vh;
            background: #ffffff;
            position: relative;
        }

        /* 隐藏的脚本注入容器 */
        #tampermonkey-container {
            display: none;
            visibility: hidden;
            position: absolute;
            top: -9999px;
            left: -9999px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面完全空白，只保留脚本注入功能 -->
        <div id="tampermonkey-container"></div>
    </div>

    <script>
        // 检查是否在APP环境中
        function isInApp() {
            const userAgent = navigator.userAgent;
            return userAgent.includes('xiaomeihua-app') ||
                   userAgent.includes('Electron') ||
                   (userAgent.includes('Chrome') && (typeof window.process !== 'undefined' ||
                    typeof window.electron !== 'undefined' ||
                    typeof window.require !== 'undefined'));
        }

        // 为APP提供的API接口（隐藏功能）
        window.aiKnowledgeAPI = {
            // 获取页面状态
            getStatus: function() {
                return {
                    loaded: true,
                    isApp: isInApp(),
                    hasTampermonkey: !!(window.xiaomeihuaAI || window.xiaomeihuaAIKnowledge || window.GM_setValue),
                    timestamp: Date.now()
                };
            },

            // 手动触发脚本检查
            checkTampermonkey: function() {
                console.log('Tampermonkey脚本检查完成');
            },

            // 添加自定义知识项（隐藏功能，不显示任何内容）
            addKnowledgeItem: function(title, content, color = '#667eea') {
                console.log('知识项已添加（隐藏）:', title);
                return true;
            }
        };

        console.log('AI知识库页面已加载（空白模式）');
    </script>

    <script>
        // 检查是否在APP环境中
        function isInApp() {
            const userAgent = navigator.userAgent;
            return userAgent.includes('xiaomeihua-app') || 
                   userAgent.includes('Electron') ||
                   (userAgent.includes('Chrome') && (typeof window.process !== 'undefined' || 
                    typeof window.electron !== 'undefined' || 
                    typeof window.require !== 'undefined'));
        }

        // Tampermonkey脚本注入功能
        function injectTampermonkeyScript() {
            if (!isInApp()) {
                console.log('非APP环境，跳过脚本注入');
                return;
            }

            console.log('开始注入Tampermonkey脚本...');
            
            // 创建脚本元素
            const script = document.createElement('script');
            script.type = 'text/javascript';
            script.innerHTML = `
                // ==UserScript==
                // @name         小梅花AI智能客服助手
                // @namespace    http://xiaomeihuakefu.cn/
                // @version      9.0.14
                // @description  智能客服助手，提供AI对话、知识库管理等功能
                // <AUTHOR>
                // @match        *://*/*
                // @grant        none
                // @run-at       document-start
                // ==/UserScript==

                (function() {
                    'use strict';
                    
                    console.log('小梅花AI智能客服助手已加载');
                    
                    // 等待页面加载完成
                    if (document.readyState === 'loading') {
                        document.addEventListener('DOMContentLoaded', initAIAssistant);
                    } else {
                        initAIAssistant();
                    }
                    
                    function initAIAssistant() {
                        console.log('初始化AI助手...');
                        
                        // 检查是否已经初始化
                        if (window.xiaomeihuaAI) {
                            console.log('AI助手已经初始化');
                            return;
                        }
                        
                        // 创建AI助手对象
                        window.xiaomeihuaAI = {
                            version: '9.0.14',
                            initialized: true,
                            
                            // 初始化函数
                            init: function() {
                                this.createFloatingButton();
                                this.bindEvents();
                                console.log('小梅花AI助手初始化完成');
                            },
                            
                            // 创建浮动按钮
                            createFloatingButton: function() {
                                const button = document.createElement('div');
                                button.id = 'xiaomeihua-ai-button';
                                button.innerHTML = '🤖';
                                button.style.cssText = \`
                                    position: fixed;
                                    bottom: 20px;
                                    right: 20px;
                                    width: 60px;
                                    height: 60px;
                                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                    border-radius: 50%;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    font-size: 24px;
                                    cursor: pointer;
                                    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                                    z-index: 999999;
                                    transition: all 0.3s ease;
                                    user-select: none;
                                \`;
                                
                                button.addEventListener('mouseenter', function() {
                                    this.style.transform = 'scale(1.1)';
                                });
                                
                                button.addEventListener('mouseleave', function() {
                                    this.style.transform = 'scale(1)';
                                });
                                
                                document.body.appendChild(button);
                            },
                            
                            // 绑定事件
                            bindEvents: function() {
                                const button = document.getElementById('xiaomeihua-ai-button');
                                if (button) {
                                    button.addEventListener('click', this.togglePanel.bind(this));
                                }
                            },
                            
                            // 切换面板显示
                            togglePanel: function() {
                                let panel = document.getElementById('xiaomeihua-ai-panel');
                                if (!panel) {
                                    this.createPanel();
                                    panel = document.getElementById('xiaomeihua-ai-panel');
                                }
                                
                                if (panel.style.display === 'none' || !panel.style.display) {
                                    panel.style.display = 'block';
                                    setTimeout(() => {
                                        panel.style.opacity = '1';
                                        panel.style.transform = 'translateY(0)';
                                    }, 10);
                                } else {
                                    panel.style.opacity = '0';
                                    panel.style.transform = 'translateY(20px)';
                                    setTimeout(() => {
                                        panel.style.display = 'none';
                                    }, 300);
                                }
                            },
                            
                            // 创建AI面板
                            createPanel: function() {
                                const panel = document.createElement('div');
                                panel.id = 'xiaomeihua-ai-panel';
                                panel.style.cssText = \`
                                    position: fixed;
                                    bottom: 90px;
                                    right: 20px;
                                    width: 350px;
                                    height: 500px;
                                    background: white;
                                    border-radius: 15px;
                                    box-shadow: 0 10px 40px rgba(0,0,0,0.3);
                                    z-index: 999998;
                                    display: none;
                                    opacity: 0;
                                    transform: translateY(20px);
                                    transition: all 0.3s ease;
                                    overflow: hidden;
                                \`;
                                
                                panel.innerHTML = \`
                                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; font-weight: bold; display: flex; justify-content: space-between; align-items: center;">
                                        <span>🤖 小梅花AI助手</span>
                                        <span style="cursor: pointer; font-size: 18px;" onclick="document.getElementById('xiaomeihua-ai-panel').style.display='none'">×</span>
                                    </div>
                                    <div style="padding: 20px; height: calc(100% - 60px); overflow-y: auto;">
                                        <div style="text-align: center; color: #666; margin-top: 50px;">
                                            <div style="font-size: 48px; margin-bottom: 20px;">🤖</div>
                                            <h3 style="margin-bottom: 10px;">AI助手已就绪</h3>
                                            <p style="line-height: 1.6;">小梅花AI智能客服助手已成功加载，可以为您提供智能对话和客服支持。</p>
                                            <div style="margin-top: 30px;">
                                                <button style="background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; margin: 5px;">开始对话</button>
                                                <button style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; margin: 5px;">设置</button>
                                            </div>
                                        </div>
                                    </div>
                                \`;
                                
                                document.body.appendChild(panel);
                            }
                        };
                        
                        // 初始化AI助手
                        window.xiaomeihuaAI.init();
                    }
                })();
            `;
            
            // 注入脚本到页面
            document.head.appendChild(script);
            console.log('Tampermonkey脚本注入完成');
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AI知识库页面加载完成');
            
            // 延迟注入脚本，确保页面完全加载
            setTimeout(function() {
                injectTampermonkeyScript();
            }, 1000);
        });

        // 立即执行检查
        if (document.readyState === 'complete') {
            setTimeout(function() {
                injectTampermonkeyScript();
            }, 500);
        }
    </script>
</body>
</html>
