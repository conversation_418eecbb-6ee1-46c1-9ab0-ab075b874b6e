<?php
// AI知识库模块 - 完整功能实现
?>

<div class="knowledge-module">
    <!-- 脚本代码输入区域 -->
    <div class="module-card">
        <div class="card-header">
            <h3><i class="fas fa-code"></i> Tampermonkey脚本管理</h3>
            <p>输入并保存Tampermonkey脚本代码，自动显示控制面板</p>
        </div>

        <div class="script-input-section">
            <div class="script-input-container">
                <label for="tampermonkey-script">脚本代码：</label>
                <textarea id="tampermonkey-script" placeholder="请粘贴完整的Tampermonkey脚本代码..." rows="10"></textarea>
            </div>

            <div class="script-actions">
                <button id="save-script-btn" class="btn btn-primary">
                    <i class="fas fa-save"></i> 保存脚本
                </button>
                <button id="clear-script-btn" class="btn btn-secondary">
                    <i class="fas fa-trash"></i> 清空
                </button>
            </div>

            <div class="script-status">
                <span id="script-status-text">未加载脚本</span>
            </div>
        </div>
    </div>
</div>

<!-- AI知识库模块专用样式 -->
<style>
.knowledge-module {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* 脚本输入区域样式 */
.script-input-section {
    padding: 20px;
}

.script-input-container {
    margin-bottom: 20px;
}

.script-input-container label {
    display: block;
    color: white;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
}

#tampermonkey-script {
    width: 100%;
    min-height: 300px;
    padding: 15px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    background: rgba(0, 0, 0, 0.3);
    color: white;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    resize: vertical;
    transition: all 0.3s ease;
}

#tampermonkey-script:focus {
    outline: none;
    border-color: #ff6b9d;
    background: rgba(0, 0, 0, 0.5);
    box-shadow: 0 0 20px rgba(255, 107, 157, 0.3);
}

#tampermonkey-script::placeholder {
    color: rgba(255, 255, 255, 0.4);
}

/* 脚本操作按钮 */
.script-actions {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.script-actions .btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 157, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.btn-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(23, 162, 184, 0.4);
}

/* 脚本状态显示 */
.script-status {
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border-left: 4px solid #ffc107;
}

#script-status-text {
    color: white;
    font-size: 14px;
    font-weight: 500;
}

.script-status.loaded {
    border-left-color: #28a745;
}

.script-status.error {
    border-left-color: #dc3545;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .script-actions {
        flex-direction: column;
    }

    .script-actions .btn {
        width: 100%;
    }

    #tampermonkey-script {
        min-height: 200px;
        font-size: 12px;
    }
}



/* 加载动画 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

/* 成功/错误状态样式 */
.status-success {
    color: #28a745 !important;
}

.status-error {
    color: #dc3545 !important;
}

.status-warning {
    color: #ffc107 !important;
}

/* ========== AI控制面板样式（来自原始脚本）========== */
.ai-control-panel {
    background: #fdfdff;
    border-radius: 16px;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    color: #2c3e50;
}

.control-buttons-section {
    margin-bottom: 20px;
}

.btn-container {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    background: white;
    padding: 15px;
    border-radius: 10px;
    border: 1px solid #e1e8f0;
}

.control-btn {
    flex: 1;
    padding: 10px 12px;
    border-radius: 8px;
    border: none;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.25s;
    text-align: center;
    min-width: 90px;
    font-size: 13px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.control-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.control-btn:active {
    transform: translateY(1px);
}

.btn-enable {
    background: linear-gradient(135deg, #09e85e 0%, #07C160 100%);
    color: white;
}

.btn-disable {
    background: linear-gradient(135deg, #ff7a7a 0%, #FA5151 100%);
    color: white;
}

.btn-upload {
    background: linear-gradient(135deg, #4facfe 0%, #10AEFF 100%);
    color: white;
}

.btn-download {
    background: linear-gradient(135deg, #a18cd1 0%, #8a6dff 100%);
    color: white;
}

.btn-ai-train {
    background: linear-gradient(135deg, #f6d365 0%, #fda085 100%);
    color: white;
}

.btn-ai-clear {
    background: linear-gradient(135deg, #98a1a9 0%, #6c757d 100%);
    color: white;
}

.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-on {
    background-color: #07C160;
    box-shadow: 0 0 8px rgba(7, 193, 96, 0.5);
}

.status-off {
    background-color: #FA5151;
}

.stats-section {
    background: #ffffff;
    border-radius: 10px;
    padding: 15px;
    font-size: 13px;
    border: 1px solid #e1e8f0;
    margin-bottom: 20px;
}

.section-title {
    font-size: 14px;
    color: #2c3e50;
    margin-bottom: 10px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.stats-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.stats-label {
    color: #5a6c80;
    width: 100px;
    font-weight: 500;
    flex-shrink: 0;
}

.stats-value {
    color: #2c3e50;
    font-weight: 600;
    flex: 1;
    display: flex;
    align-items: center;
}

.cs-name-input {
    width: 100%;
    padding: 8px;
    border-radius: 8px;
    border: 1px solid #e1e8f0;
    font-size: 13px;
    background-color: #f0f4f8;
    color: #5a6c80;
    cursor: not-allowed;
}

.ai-settings-section {
    background: #ffffff;
    border-radius: 10px;
    padding: 15px;
    border: 1px solid #e1e8f0;
    margin-bottom: 20px;
}

.ai-settings-row {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;
}

.ai-toggle-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.ai-api-input {
    flex: 1;
    padding: 8px;
    border-radius: 6px;
    border: 1px solid #e1e8f0;
    font-size: 13px;
}

.ai-model-select {
    padding: 8px;
    border-radius: 6px;
    border: 1px solid #e1e8f0;
    font-size: 13px;
    background: white;
}

.switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 20px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #07C160;
}

input:checked + .slider:before {
    transform: translateX(20px);
}

.ai-info-tip {
    font-size: 12px;
    color: #5a6c80;
    margin-top: 5px;
    padding: 5px;
    background: #f8f9ff;
    border-radius: 6px;
}

.ai-status {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-top: 5px;
}

.ai-status-text {
    font-size: 12px;
    color: #5a6c80;
}

.ai-status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #FA5151;
}

.ai-status-indicator.active {
    background-color: #07C160;
}

.ai-knowledge-section {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.ai-knowledge-panel {
    margin-top: 10px;
    border: 1px solid #e1e8f0;
    border-radius: 8px;
    padding: 10px;
    background: #f8f9ff;
}

.ai-knowledge-stats {
    font-size: 12px;
    color: #5a6c80;
    margin-top: 5px;
    text-align: center;
}

.templates-section {
    background: #ffffff;
    border-radius: 10px;
    padding: 15px;
    border: 1px solid #e1e8f0;
}

.template-list {
    border: 1px solid #e1e8f0;
    border-radius: 8px;
    max-height: 200px;
    overflow-y: auto;
    padding: 8px;
    background: #fafcff;
    margin-bottom: 15px;
}

.template-item {
    padding: 10px 12px;
    border-bottom: 1px solid #f0f4f8;
    display: flex;
    flex-direction: column;
    transition: all 0.2s;
    border-radius: 6px;
    margin: 3px 0;
    background: #f9fbfd;
}

.template-item:hover {
    background: #f0f7ff;
    transform: translateX(2px);
}

.template-item:last-child {
    border-bottom: none;
}

.template-keywords {
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 6px;
    flex-wrap: wrap;
}

.keyword-tag {
    background: #e8f4ff;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.template-reply {
    color: #4a6583;
    font-size: 13px;
    line-height: 1.4;
    padding-left: 4px;
}

.empty-templates {
    text-align: center;
    color: #a3b1c2;
    padding: 20px;
    font-size: 13px;
}
</style>

<!-- AI知识库模块专用JavaScript -->
<script>
// 存储当前脚本内容
let currentScript = '';
let scriptLoaded = false;

// AI知识库模块初始化
document.addEventListener('DOMContentLoaded', function() {
    initKnowledgeModule();
});

function initKnowledgeModule() {
    console.log('AI知识库模块初始化完成');

    // 绑定事件监听器
    bindEventListeners();

    // 尝试加载已保存的脚本
    loadSavedScript();
}

function bindEventListeners() {
    // 保存脚本按钮
    document.getElementById('save-script-btn').addEventListener('click', saveScript);

    // 清空按钮
    document.getElementById('clear-script-btn').addEventListener('click', clearScript);

    // 脚本输入框变化监听
    document.getElementById('tampermonkey-script').addEventListener('input', function() {
        updateScriptStatus('已修改，未保存', 'warning');
    });
}

function saveScript() {
    const scriptContent = document.getElementById('tampermonkey-script').value.trim();

    if (!scriptContent) {
        showToast('请输入脚本代码', 'error');
        return;
    }

    // 验证脚本格式
    if (!validateScript(scriptContent)) {
        showToast('脚本格式不正确，请检查是否为有效的Tampermonkey脚本', 'error');
        return;
    }

    // 保存到localStorage
    try {
        localStorage.setItem('ai_knowledge_script', scriptContent);
        currentScript = scriptContent;
        scriptLoaded = true;

        updateScriptStatus('脚本已保存', 'success');
        showToast('脚本保存成功！', 'success');

    } catch (error) {
        console.error('保存脚本失败:', error);
        showToast('保存脚本失败，请重试', 'error');
    }
}

function clearScript() {
    if (confirm('确定要清空脚本代码吗？')) {
        document.getElementById('tampermonkey-script').value = '';
        localStorage.removeItem('ai_knowledge_script');
        currentScript = '';
        scriptLoaded = false;

        updateScriptStatus('未加载脚本', 'warning');
        showToast('脚本已清空', 'info');
    }
}

function loadSavedScript() {
    try {
        const savedScript = localStorage.getItem('ai_knowledge_script');
        if (savedScript) {
            document.getElementById('tampermonkey-script').value = savedScript;
            currentScript = savedScript;
            scriptLoaded = true;
            updateScriptStatus('脚本已加载', 'success');
        }
    } catch (error) {
        console.error('加载保存的脚本失败:', error);
    }
}

function validateScript(script) {
    // 基本验证：检查是否包含Tampermonkey脚本的基本结构
    return script.includes('// ==UserScript==') &&
           script.includes('// ==/UserScript==') &&
           script.includes('@name') &&
           script.includes('@version');
}

function updateScriptStatus(message, type) {
    const statusElement = document.getElementById('script-status-text');
    const statusContainer = statusElement.parentElement;

    statusElement.textContent = message;

    // 移除所有状态类
    statusContainer.classList.remove('loaded', 'error');

    // 添加对应状态类
    if (type === 'success') {
        statusContainer.classList.add('loaded');
    } else if (type === 'error') {
        statusContainer.classList.add('error');
    }
}



// 当标签切换到知识库时触发
window.addEventListener('tabChanged', function(e) {
    if (e.detail.tab === 'knowledge') {
        console.log('切换到AI知识库页面');
    }
});
</script>
