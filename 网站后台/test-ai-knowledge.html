<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI知识库API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #005a87;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 4px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #4caf50;
        }
        .error {
            border-left: 4px solid #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI知识库API测试</h1>
        
        <div class="test-section">
            <h3>1. 测试基本API连接</h3>
            <button onclick="testBasicAPI()">测试基本连接</button>
            <div id="basic-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 测试脚本获取（模拟APP访问）</h3>
            <button onclick="testScriptAPI()">获取脚本内容</button>
            <div id="script-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 测试访问控制（模拟浏览器访问）</h3>
            <button onclick="testAccessControl()">测试访问控制</button>
            <div id="access-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>4. 测试AI知识库页面</h3>
            <button onclick="openAIKnowledge()">打开AI知识库页面</button>
            <div id="page-result" class="result"></div>
        </div>
    </div>

    <script>
        // 测试基本API连接
        async function testBasicAPI() {
            const resultDiv = document.getElementById('basic-result');
            resultDiv.textContent = '正在测试...';
            
            try {
                const response = await fetch('./api/knowledge.php?action=categories');
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ API连接成功\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ API连接失败: ${error.message}`;
            }
        }
        
        // 测试脚本获取（模拟APP访问）
        async function testScriptAPI() {
            const resultDiv = document.getElementById('script-result');
            resultDiv.textContent = '正在获取脚本...';
            
            try {
                const response = await fetch('./api/knowledge.php?action=script', {
                    headers: {
                        'X-App-Token': 'xiaomeihua-ai-knowledge-2025',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 xiaomeihua-app/1.0 Electron'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    const script = data.knowledge;
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 脚本获取成功
脚本名称: ${script.script_name}
脚本版本: ${script.script_version}
更新时间: ${script.last_updated}
文件大小: ${script.file_size ? Math.round(script.file_size / 1024 * 100) / 100 + 'KB' : '未知'}
代码行数: ${script.script_lines || '未知'}
脚本内容长度: ${script.script_content ? script.script_content.length : 0} 字符`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 脚本获取失败: ${data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 脚本获取失败: ${error.message}`;
            }
        }
        
        // 测试访问控制（模拟浏览器访问）
        async function testAccessControl() {
            const resultDiv = document.getElementById('access-result');
            resultDiv.textContent = '正在测试访问控制...';
            
            try {
                const response = await fetch('./api/knowledge.php?action=script');
                const data = await response.json();
                
                if (response.status === 403 || (data.success === false && data.message.includes('访问被拒绝'))) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 访问控制正常工作\n响应: ${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 访问控制可能有问题\n响应: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 测试失败: ${error.message}`;
            }
        }
        
        // 打开AI知识库页面
        function openAIKnowledge() {
            const resultDiv = document.getElementById('page-result');
            resultDiv.className = 'result';
            resultDiv.textContent = '正在打开AI知识库页面...';
            
            try {
                window.open('./ai-knowledge.html', '_blank');
                resultDiv.className = 'result success';
                resultDiv.textContent = '✅ AI知识库页面已在新窗口中打开';
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 打开页面失败: ${error.message}`;
            }
        }
        
        // 页面加载完成后自动运行基本测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AI知识库API测试页面已加载');
        });
    </script>
</body>
</html>
