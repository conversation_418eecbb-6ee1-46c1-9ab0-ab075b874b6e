# URL匹配功能部署指南

## 问题解决方案

### 1. 数据库导入异常解决

**问题：** `database_url_patterns_update.sql` 导入失败

**解决方案：** 使用简化版数据库脚本

#### 方法一：使用简化脚本
执行 `database_url_patterns_update_simple.sql`：

```sql
-- 简化版数据库更新脚本
ALTER TABLE `scripts` ADD COLUMN `url_patterns` text DEFAULT NULL COMMENT 'URL匹配规则，JSON格式存储多个URL';
ALTER TABLE `scripts` ADD INDEX `idx_url_patterns` (`url_patterns`(255));
DESCRIBE `scripts`;
```

#### 方法二：手动执行SQL
如果自动脚本仍然失败，请手动执行以下SQL语句：

```sql
-- 1. 检查字段是否已存在
SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'scripts' 
  AND COLUMN_NAME = 'url_patterns';

-- 2. 如果字段不存在，添加字段
ALTER TABLE `scripts` ADD COLUMN `url_patterns` text DEFAULT NULL COMMENT 'URL匹配规则，JSON格式存储多个URL';

-- 3. 添加索引（可选）
ALTER TABLE `scripts` ADD INDEX `idx_url_patterns` (`url_patterns`(255));

-- 4. 验证结果
DESCRIBE `scripts`;
```

### 2. 网站后台URL输入功能缺失解决

**问题：** 网站后台添加脚本页面没有URL输入框

**原因分析：**
- 可能访问的是 `scripts_optimized.php` 而不是 `scripts.php`
- 或者访问的是其他版本的脚本管理页面

**解决方案：**

#### 确认访问的页面
检查您当前访问的URL，确认是以下哪个页面：
- `index.php?page=scripts` (使用 scripts.php)
- `index.php?page=scripts_optimized` (使用 scripts_optimized.php)

#### 已更新的文件列表
以下文件已经包含完整的URL匹配功能：

1. **主要脚本管理页面：**
   - `网站后台/xuxuemei/templates/scripts.php` ✅
   - `网站后台/deploy/templates/scripts.php` ✅

2. **优化版脚本管理页面：**
   - `网站后台/xuxuemei/templates/scripts_optimized.php` ✅
   - `网站后台/deploy/templates/scripts_optimized.php` ✅

#### 功能特性确认
更新后的页面应该包含：
- ✅ "自定义URL匹配" 标签（红色*必填标识）
- ✅ URL输入框（支持多个）
- ✅ "添加URL" 按钮
- ✅ URL删除按钮
- ✅ URL格式验证
- ✅ 提示信息和示例

## 部署步骤

### 第一步：数据库更新
1. 备份现有数据库
2. 执行数据库更新脚本：
   ```bash
   mysql -u用户名 -p数据库名 < database_url_patterns_update_simple.sql
   ```
3. 验证字段添加成功：
   ```sql
   DESCRIBE scripts;
   ```

### 第二步：文件更新
确保以下文件已更新到最新版本：
- `网站后台/xuxuemei/templates/scripts.php`
- `网站后台/xuxuemei/templates/scripts_optimized.php`
- `网站后台/deploy/templates/scripts.php`
- `网站后台/deploy/templates/scripts_optimized.php`
- `网站后台/api/verify.php`
- `网站后台/deploy/verify.php`

### 第三步：清除缓存
1. 清除浏览器缓存
2. 如果使用了PHP缓存，重启PHP服务
3. 刷新页面

### 第四步：功能测试
1. 访问脚本管理页面
2. 点击"添加脚本"
3. 确认看到"自定义URL匹配"区域
4. 测试添加多个URL规则
5. 测试URL格式验证

## 故障排除

### 问题1：页面没有URL输入框
**解决方案：**
1. 确认访问的是正确的页面URL
2. 检查浏览器控制台是否有JavaScript错误
3. 强制刷新页面（Ctrl+F5）
4. 检查文件是否正确上传

### 问题2：URL验证不工作
**解决方案：**
1. 检查JavaScript是否正确加载
2. 查看浏览器控制台错误信息
3. 确认表单ID为 `scriptForm`

### 问题3：保存脚本时出错
**解决方案：**
1. 检查数据库字段是否正确添加
2. 查看PHP错误日志
3. 确认URL规则格式正确

## 测试用例

### URL匹配规则测试
使用以下URL规则进行测试：

1. **精确匹配：**
   ```
   https://store.weixin.qq.com/shop/home
   ```

2. **通配符匹配：**
   ```
   *://store.weixin.qq.com/*
   https://store.weixin.qq.com/*
   ```

3. **多规则测试：**
   ```
   https://store.weixin.qq.com/*
   *://example.com/shop/*
   https://test.com/admin/*
   ```

### 验证步骤
1. 添加包含上述URL规则的脚本
2. 检查数据库中 `url_patterns` 字段是否正确存储JSON数据
3. 使用APP访问对应URL，确认脚本正确加载
4. 访问不匹配的URL，确认脚本不加载

## 联系支持

如果按照以上步骤仍然无法解决问题，请提供：
1. 当前访问的页面URL
2. 浏览器控制台错误信息
3. PHP错误日志
4. 数据库表结构截图

这将帮助快速定位和解决问题。
