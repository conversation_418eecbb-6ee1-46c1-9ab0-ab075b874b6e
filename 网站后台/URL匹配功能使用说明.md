# URL匹配功能使用说明

## 功能概述

本次更新为网站后台脚本管理系统和APP软件添加了URL匹配功能，实现了脚本的精准页面加载控制。

## 主要特性

### 1. 网站后台功能
- 在"添加脚本"页面的描述字段下方新增"自定义URL匹配"功能
- 支持输入多个URL匹配规则
- URL匹配规则为必填项，确保脚本只在指定页面加载
- 支持通配符 `*` 进行模糊匹配

### 2. APP软件功能
- APP软件会根据后台设置的URL匹配规则进行脚本加载
- 只有当前页面URL匹配规则时，才会加载和执行脚本
- 不匹配的页面不会加载脚本，提高性能和安全性

## URL匹配规则说明

### 基本语法
- `*` - 通配符，匹配任意字符
- 支持协议通配符：`*://example.com/*`
- 支持路径通配符：`https://example.com/*`
- 支持精确匹配：`https://example.com/specific/path`

### 示例规则

#### 1. 微信小商店匹配
```
*://store.weixin.qq.com/*
https://store.weixin.qq.com/*
https://store.weixin.qq.com/shop/*
```

#### 2. 特定页面匹配
```
https://store.weixin.qq.com/shop/home
https://example.com/admin/dashboard
```

#### 3. 多域名匹配
```
*://*.example.com/*
https://subdomain.example.com/*
```

#### 4. 全站匹配（不推荐）
```
*
```

## 使用步骤

### 1. 网站后台操作

1. 登录网站后台管理系统
2. 进入"脚本管理"页面
3. 点击"添加脚本"或编辑现有脚本
4. 填写脚本基本信息（名称、版本、描述）
5. 在"自定义URL匹配"区域输入URL规则：
   - 至少输入一个有效的URL匹配规则
   - 点击"添加URL"按钮可添加多个规则
   - 点击红色删除按钮可移除不需要的规则
6. 输入脚本代码
7. 点击"保存脚本"

### 2. APP软件使用

1. 启动APP软件并登录
2. 访问网页时，APP会自动检查当前页面URL
3. 如果URL匹配后台设置的规则，脚本会自动加载
4. 如果URL不匹配，脚本不会加载，控制台会显示相关日志

## 技术实现

### 数据库更改
- 为 `scripts` 表添加了 `url_patterns` 字段
- 使用JSON格式存储多个URL匹配规则

### API接口更改
- `verify.php` 接口现在返回 `url_patterns` 字段
- APP可以获取到脚本的URL匹配规则

### APP逻辑更改
- 脚本注入前会进行URL匹配检查
- 支持缓存脚本的URL规则验证
- 添加了详细的匹配日志输出

## 兼容性说明

### 向后兼容
- 现有脚本如果没有设置URL匹配规则，默认允许在所有页面加载
- 不会影响现有脚本的正常运行

### 升级建议
1. 运行数据库更新脚本：`database_url_patterns_update.sql`
2. 为现有脚本添加适当的URL匹配规则
3. 测试脚本在目标页面的加载情况

## 故障排除

### 常见问题

#### 1. 脚本不加载
- 检查URL匹配规则是否正确
- 查看浏览器控制台的匹配日志
- 确认当前页面URL是否符合规则

#### 2. URL规则不生效
- 确认规则格式正确（包含协议）
- 检查通配符使用是否正确
- 验证数据库中的规则是否正确保存

#### 3. 性能问题
- 避免使用过于宽泛的匹配规则
- 合理设置URL规则，避免不必要的脚本加载

### 调试方法

1. 打开浏览器开发者工具
2. 查看控制台输出的匹配日志
3. 使用测试页面 `test_url_matching.html` 验证匹配逻辑
4. 检查网络请求中的 `url_patterns` 字段

## 安全注意事项

1. URL匹配规则应尽可能具体，避免过度宽泛
2. 定期审查脚本的URL匹配规则
3. 避免在敏感页面加载不必要的脚本
4. 测试脚本在不同URL下的行为

## 更新日志

### v1.0.0 (2025-08-10)
- 新增URL匹配功能
- 支持多URL规则配置
- 实现APP端精准匹配加载
- 添加向后兼容支持
