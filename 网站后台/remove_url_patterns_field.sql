-- 删除URL匹配功能相关的数据库字段
-- 执行时间: 2025-08-10
-- 功能: 删除scripts表中的url_patterns字段和相关索引

-- 检查并删除索引（如果存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
       AND TABLE_NAME = 'scripts'
       AND INDEX_NAME = 'idx_url_patterns') > 0,
    'DROP INDEX `idx_url_patterns` ON `scripts`',
    'SELECT "索引idx_url_patterns不存在，跳过删除" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并删除url_patterns字段（如果存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
       AND TABLE_NAME = 'scripts'
       AND COLUMN_NAME = 'url_patterns') > 0,
    'ALTER TABLE `scripts` DROP COLUMN `url_patterns`',
    'SELECT "字段url_patterns不存在，跳过删除" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证字段是否删除成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'scripts' 
    AND COLUMN_NAME = 'url_patterns';

-- 如果上面的查询没有返回任何结果，说明字段已成功删除

-- 显示当前scripts表结构
DESCRIBE `scripts`;
