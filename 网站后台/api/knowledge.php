<?php
/**
 * 独立知识库API接口
 * 处理APP知识库相关请求
 */

// 引入统一数据库配置
require_once __DIR__ . '/database_config.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, User-Agent');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 错误处理
function sendError($message, $code = 500) {
    http_response_code($code);
    echo json_encode([
        'success' => false,
        'message' => $message,
        'knowledge' => null,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit;
}

// 成功响应
function sendSuccess($data, $message = 'Success') {
    echo json_encode([
        'success' => true,
        'message' => $message,
        'knowledge' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit;
}

// 获取数据库连接
function getDatabase() {
    try {
        $dsn = "mysql:host=127.0.0.1;port=3306;dbname=xiaomeihuakefu_c;charset=utf8mb4";
        $pdo = new PDO($dsn, 'xiaomeihuakefu_c', '7Da5F1Xx995cxYz8', [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::ATTR_TIMEOUT => 10
        ]);

        // 设置时区
        $pdo->exec("SET time_zone = '+08:00'");

        return $pdo;
    } catch (PDOException $e) {
        // 数据库连接失败时返回null，使用备用数据
        return null;
    }
}

// 初始化知识库表
function initKnowledgeTable($db) {
    try {
        $sql = "CREATE TABLE IF NOT EXISTS app_knowledge (
            id INT AUTO_INCREMENT PRIMARY KEY,
            category VARCHAR(100) NOT NULL,
            title VARCHAR(255) NOT NULL,
            content TEXT NOT NULL,
            tags VARCHAR(500),
            priority INT DEFAULT 1,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_category (category),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        $db->exec($sql);
        
        // 检查是否有默认数据
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM app_knowledge");
        $stmt->execute();
        $result = $stmt->fetch();
        
        if ($result['count'] == 0) {
            // 插入默认知识库内容
            $knowledgeItems = [
                [
                    'category' => '使用指南',
                    'title' => '如何开始使用小梅花AI智能客服',
                    'content' => '<h3>快速开始</h3><p>欢迎使用小梅花AI智能客服！以下是快速开始指南：</p><ol><li>登录您的账户</li><li>选择或创建店铺</li><li>配置AI助手设置</li><li>开始与客户互动</li></ol><h3>主要功能</h3><ul><li>智能对话：AI自动回复客户咨询</li><li>知识库管理：维护常见问题和答案</li><li>多店铺支持：管理多个店铺的客服</li><li>数据分析：查看客服效果统计</li></ul>',
                    'tags' => '新手指南,快速开始,使用教程',
                    'priority' => 10
                ],
                [
                    'category' => '功能介绍',
                    'title' => 'AI智能对话功能详解',
                    'content' => '<h3>AI智能对话</h3><p>小梅花AI智能客服采用先进的自然语言处理技术，能够：</p><ul><li>理解客户意图</li><li>提供准确回答</li><li>学习优化回复</li><li>支持多轮对话</li></ul><h3>配置方法</h3><ol><li>进入AI设置页面</li><li>配置知识库内容</li><li>设置回复策略</li><li>测试对话效果</li></ol>',
                    'tags' => 'AI对话,智能客服,自然语言处理',
                    'priority' => 9
                ],
                [
                    'category' => '常见问题',
                    'title' => '如何解决登录问题',
                    'content' => '<h3>登录问题解决方案</h3><p>如果您遇到登录问题，请尝试以下解决方案：</p><ol><li>检查网络连接</li><li>清除浏览器缓存</li><li>确认用户名和密码</li><li>联系技术支持</li></ol><h3>常见错误</h3><ul><li>密码错误：请重置密码</li><li>账户被锁定：联系管理员</li><li>网络超时：检查网络设置</li></ul>',
                    'tags' => '登录问题,故障排除,技术支持',
                    'priority' => 8
                ],
                [
                    'category' => '技术支持',
                    'title' => '联系我们获取帮助',
                    'content' => '<h3>技术支持</h3><p>如果您需要帮助，可以通过以下方式联系我们：</p><ul><li>在线客服：工作日9:00-18:00</li><li>邮箱支持：<EMAIL></li><li>电话支持：400-123-4567</li><li>QQ群：123456789</li></ul><h3>常见问题</h3><p>访问我们的帮助中心查看更多常见问题和解决方案。</p>',
                    'tags' => '技术支持,联系方式,客服',
                    'priority' => 7
                ]
            ];
            
            foreach ($knowledgeItems as $item) {
                $stmt = $db->prepare("INSERT INTO app_knowledge (category, title, content, tags, priority) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute([
                    $item['category'],
                    $item['title'],
                    $item['content'],
                    $item['tags'],
                    $item['priority']
                ]);
            }
        }
    } catch (Exception $e) {
        // 忽略表创建错误，继续执行
    }
}

// 获取知识库内容
function getKnowledge($category = null, $search = null) {
    $db = getDatabase();

    // 如果数据库连接失败，使用备用数据
    if (!$db) {
        return getFallbackKnowledge($category, $search);
    }

    initKnowledgeTable($db);

    try {
        $sql = "SELECT * FROM app_knowledge WHERE status = 'active'";
        $params = [];

        if ($category) {
            $sql .= " AND category = ?";
            $params[] = $category;
        }

        if ($search) {
            $sql .= " AND (title LIKE ? OR content LIKE ? OR tags LIKE ?)";
            $searchTerm = "%{$search}%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        $sql .= " ORDER BY priority DESC, created_at DESC";

        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        $knowledge = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return $knowledge ?: getFallbackKnowledge($category, $search);

    } catch (Exception $e) {
        // 查询失败时使用备用数据
        return getFallbackKnowledge($category, $search);
    }
}

// 获取知识库分类
function getKnowledgeCategories() {
    $db = getDatabase();

    // 如果数据库连接失败，使用备用数据
    if (!$db) {
        return getFallbackCategories();
    }

    initKnowledgeTable($db);

    try {
        $stmt = $db->prepare("SELECT DISTINCT category FROM app_knowledge WHERE status = 'active' ORDER BY category");
        $stmt->execute();
        $categories = $stmt->fetchAll(PDO::FETCH_COLUMN);

        return $categories ?: getFallbackCategories();

    } catch (Exception $e) {
        // 查询失败时使用备用数据
        return getFallbackCategories();
    }
}

// 获取备用知识库数据
function getFallbackKnowledge($category = null, $search = null) {
    $knowledgeItems = [
        [
            'id' => 1,
            'category' => '使用指南',
            'title' => '如何开始使用小梅花AI智能客服',
            'content' => '<h3>快速开始</h3><p>欢迎使用小梅花AI智能客服！以下是快速开始指南：</p><ol><li>登录您的账户</li><li>选择或创建店铺</li><li>配置AI助手设置</li><li>开始与客户互动</li></ol><h3>主要功能</h3><ul><li>智能对话：AI自动回复客户咨询</li><li>知识库管理：维护常见问题和答案</li><li>多店铺支持：管理多个店铺的客服</li><li>数据分析：查看客服效果统计</li></ul>',
            'tags' => '新手指南,快速开始,使用教程',
            'priority' => 10,
            'status' => 'active',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ],
        [
            'id' => 2,
            'category' => '功能介绍',
            'title' => 'AI智能对话功能详解',
            'content' => '<h3>AI智能对话</h3><p>小梅花AI智能客服采用先进的自然语言处理技术，能够：</p><ul><li>理解客户意图</li><li>提供准确回答</li><li>学习优化回复</li><li>支持多轮对话</li></ul><h3>配置方法</h3><ol><li>进入AI设置页面</li><li>配置知识库内容</li><li>设置回复策略</li><li>测试对话效果</li></ol>',
            'tags' => 'AI对话,智能客服,自然语言处理',
            'priority' => 9,
            'status' => 'active',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ],
        [
            'id' => 3,
            'category' => '常见问题',
            'title' => '如何解决登录问题',
            'content' => '<h3>登录问题解决方案</h3><p>如果您遇到登录问题，请尝试以下解决方案：</p><ol><li>检查网络连接</li><li>清除浏览器缓存</li><li>确认用户名和密码</li><li>联系技术支持</li></ol><h3>常见错误</h3><ul><li>密码错误：请重置密码</li><li>账户被锁定：联系管理员</li><li>网络超时：检查网络设置</li></ul>',
            'tags' => '登录问题,故障排除,技术支持',
            'priority' => 8,
            'status' => 'active',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ],
        [
            'id' => 4,
            'category' => '技术支持',
            'title' => '联系我们获取帮助',
            'content' => '<h3>技术支持</h3><p>如果您需要帮助，可以通过以下方式联系我们：</p><ul><li>在线客服：工作日9:00-18:00</li><li>邮箱支持：<EMAIL></li><li>电话支持：400-123-4567</li><li>QQ群：123456789</li></ul><h3>常见问题</h3><p>访问我们的帮助中心查看更多常见问题和解决方案。</p>',
            'tags' => '技术支持,联系方式,客服',
            'priority' => 7,
            'status' => 'active',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]
    ];

    // 过滤分类
    if ($category) {
        $knowledgeItems = array_filter($knowledgeItems, function($item) use ($category) {
            return $item['category'] === $category;
        });
    }

    // 搜索过滤
    if ($search) {
        $knowledgeItems = array_filter($knowledgeItems, function($item) use ($search) {
            return stripos($item['title'], $search) !== false ||
                   stripos($item['content'], $search) !== false ||
                   stripos($item['tags'], $search) !== false;
        });
    }

    return array_values($knowledgeItems);
}

// 获取备用分类数据
function getFallbackCategories() {
    return ['使用指南', '功能介绍', '常见问题', '技术支持'];
}

// 获取AI知识库脚本代码
function getAIKnowledgeScript() {
    // 读取AI知识库开发目录中的脚本文件
    $scriptPath = __DIR__ . '/../AI知识库开发';

    if (!file_exists($scriptPath) || !is_readable($scriptPath)) {
        error_log("AI知识库脚本文件不存在或不可读: " . $scriptPath);
        return getFallbackScript();
    }

    $scriptContent = file_get_contents($scriptPath);

    if ($scriptContent === false) {
        error_log("无法读取AI知识库脚本文件: " . $scriptPath);
        return getFallbackScript();
    }

    // 提取脚本版本信息
    $version = '9.0.14';
    $name = '小梅花智能AI客服';

    if (preg_match('/@version\s+([^\r\n]+)/', $scriptContent, $matches)) {
        $version = trim($matches[1]);
    }

    if (preg_match('/@name\s+([^\r\n]+)/', $scriptContent, $matches)) {
        $name = trim($matches[1]);
    }

    return [
        'script_content' => $scriptContent,
        'script_version' => $version,
        'script_name' => $name,
        'last_updated' => date('Y-m-d H:i:s', filemtime($scriptPath)),
        'file_size' => filesize($scriptPath),
        'script_lines' => substr_count($scriptContent, "\n") + 1
    ];
}

// 获取备用脚本内容
function getFallbackScript() {
    return [
        'script_content' => '// 小梅花智能AI客服脚本\n// 版本: 9.0.14\n// 备用脚本内容',
        'script_version' => '9.0.14',
        'script_name' => '小梅花智能AI客服',
        'last_updated' => date('Y-m-d H:i:s')
    ];
}

// 验证APP访问权限
function validateAppAccess() {
    // 记录访问信息用于调试
    error_log("API访问验证 - User-Agent: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'N/A'));
    error_log("API访问验证 - X-App-Token: " . ($_SERVER['HTTP_X_APP_TOKEN'] ?? 'N/A'));
    error_log("API访问验证 - Referer: " . ($_SERVER['HTTP_REFERER'] ?? 'N/A'));

    // 检查User-Agent是否来自我们的APP
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

    // 更宽松的User-Agent检查
    $isValidUserAgent = strpos($userAgent, 'xiaomeihua-app') !== false ||
                       strpos($userAgent, 'Electron') !== false ||
                       strpos($userAgent, 'Chrome') !== false; // 允许Chrome（Electron基于Chrome）

    if (!$isValidUserAgent) {
        error_log("API访问验证失败 - User-Agent不匹配: " . $userAgent);
        return false;
    }

    // 检查Referer是否来自我们的域名
    $referer = $_SERVER['HTTP_REFERER'] ?? '';
    $isValidReferer = empty($referer) ||
                     strpos($referer, 'xiaomeihuakefu.cn') !== false ||
                     strpos($referer, 'localhost') !== false ||
                     strpos($referer, '127.0.0.1') !== false;

    if (!$isValidReferer) {
        error_log("API访问验证失败 - Referer不匹配: " . $referer);
        return false;
    }

    // 检查token（可选）
    $appToken = $_SERVER['HTTP_X_APP_TOKEN'] ?? '';
    if (!empty($appToken) && $appToken !== 'xiaomeihua-ai-knowledge-2025') {
        error_log("API访问验证失败 - Token不匹配: " . $appToken);
        return false;
    }

    error_log("API访问验证成功");
    return true;
}

// 主处理逻辑
try {
    // 只处理GET请求
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        sendError('只支持GET请求', 405);
    }

    // 获取参数
    $action = $_GET['action'] ?? 'list';
    $category = $_GET['category'] ?? null;
    $search = $_GET['search'] ?? null;

    switch ($action) {
        case 'list':
            $knowledge = getKnowledge($category, $search);
            sendSuccess($knowledge, 'Knowledge list retrieved');
            break;

        case 'categories':
            $categories = getKnowledgeCategories();
            sendSuccess($categories, 'Knowledge categories retrieved');
            break;

        case 'script':
            // 验证APP访问权限
            if (!validateAppAccess()) {
                sendError('访问被拒绝：只允许APP访问', 403);
            }

            $script = getAIKnowledgeScript();
            sendSuccess($script, 'AI Knowledge script retrieved');
            break;

        default:
            sendError('无效的操作: ' . $action, 400);
    }

} catch (Exception $e) {
    sendError('服务器内部错误: ' . $e->getMessage());
}
?>
